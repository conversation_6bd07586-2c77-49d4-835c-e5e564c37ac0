import {
  NavigationPointer01Icon,
  ActivityIcon,
  ClockRefreshIcon,
} from "@everstage/evericons/outlined";

/**
 * Standard date format strings used throughout the application
 * for consistent date display
 */
export const COMMON_MOMENT_DATE_FORMAT = "MMM DD, YYYY";
export const COMMON_FNS_DATE_FORMAT = "MMM dd, yyyy";

/**
 * Data type definitions used for variable typing throughout the application
 * These are used for validation, display formatting, and database mappings
 */
export const DATATYPE = {
  INTEGER: "Integer", // Numeric values
  STRING: "String", // Text values
  DATE: "Date", // Date values
  BOOLEAN: "Boolean", // True/false values
  OBJECT: "Object", // Complex object values
  PERCENTAGE: "Percentage", // Percentage values (formatted differently from regular numbers)
  DAYDURATION: "DayDuration", // Duration measured in days
  MINUTEDURATION: "MinuteDuration", // Duration measured in minutes
  SECONDDURATION: "SecondDuration", // Duration measured in seconds
  ARRAY: "Array", // Array of mixed values
  INTARRAY: "IntArray", // Array of integers
  STRINGARRAY: "StringArray", // Array of strings
  EMAIL: "Email", // Email address format
  HIERARCHY: "Hierarchy", // Hierarchical data structure
};

/**
 * Subset of data types that can be used with the Coalesce function
 * which returns the first non-null value in a list
 */
export const COALESCE_DATATYPES = [
  { label: DATATYPE.STRING, value: DATATYPE.STRING },
  { label: DATATYPE.DATE, value: DATATYPE.DATE },
  { label: DATATYPE.BOOLEAN, value: DATATYPE.BOOLEAN },
  { label: DATATYPE.EMAIL, value: DATATYPE.EMAIL },
];

/**
 * Special data types used in specific commission calculation scenarios
 */
export const SPLDATATYPE = {
  QUOTA: "Quota", // Quota target values
  ACONFIG: "AConfig", // Config values for calculations
};

/**
 * Token types used in the expression parser system
 * These define the grammar for commission formulas and calculations
 */
export const TOKENTYPE = {
  VARIABLE: "VARIABLE", // Variables in expressions (e.g., deal.amount)
  OPERATOR: "OPERATOR", // Mathematical operators (e.g., +, -, *, /)
  LBRACKET: "LBRACKET", // Left bracket for grouping
  RBRACKET: "RBRACKET", // Right bracket for grouping
  FUNCTION: "FUNCTION", // Function calls (e.g., SUM, COUNT)
};

/**
 * Token subtypes providing more detailed categorization
 * for the expression parser system
 */
export const TOKENSUBTYPE = {
  ARITHMETIC: "ARITHMETIC", // Arithmetic operators like +, -, *, /
  LOGICAL: "LOGICAL", // Logical operators like AND, OR, NOT
  LBRACKET: "LBRACKET", // Left bracket for grouping
  RBRACKET: "RBRACKET", // Right bracket for grouping
  PRIMARY: "PRIMARY", // Primary variables from source data
  SECONDARY: "SECONDARY", // Secondary/derived variables
  DERIVED: "DERIVED", // Calculated variables
  CONFIG: "CONFIG", // Configuration values
  CALCUALTED: "CALCUALTED", // Calculated values (note: typo in original)
};

/**
 * Categories for tokens in the expression system
 * Differentiates between static values and dynamic calculations
 */
export const TOKENCATEGORY = {
  DYNAMIC: "DYNAMIC", // Values that change or are calculated
  STATIC: "STATIC", // Fixed values
};

/**
 * Actions available in the expression editor
 */
export const ACTIONS = {
  WRAP: "WRAP", // Wrap an expression in a function or brackets
};

/**
 * Status states for formula expression validation
 */
export const EXPR_STATUS = {
  EMPTY: "EMPTY", // No expression provided
  CHECKING: "CHECKING", // Currently validating
  VALID: "VALID", // Expression is valid
  INVALID: "INVALID", // Expression is invalid
};

/**
 * Primary object types in the data model
 * These represent the core business entities
 */
export const PRIMARYOBJECT = {
  ACCOUNT: "Account", // Customer account
  OPPORTUNITY: "Opportunity", // Sales opportunity/deal
  LEAD: "Lead", // Prospective customer
  ACTIVITY: "Activity", // User/sales activity
};

/**
 * Maps filter types to their corresponding object types
 * Used for filtering data in the application
 */
export const AMONGTYPE = {
  ClosedWonDeals: "Opportunity",
  CreatedDeals: "Opportunity",
  OpenDeals: "Opportunity",
  ClosedLostDeals: "Opportunity",
  QualifiedLeads: "Lead",
  CreatedLeads: "Lead",
  Accounts: "Account",
  CreatedActivities: "Activity",
};

/**
 * Types of functions available in the formula system
 * Categorizes functions by their behavior and input/output patterns
 */
export const FUNCTIONTYPES = {
  TOKEN: "TOKEN", // Token-based functions
  SIMPLE: "SIMPLE", // Simple functions with direct inputs/outputs
  CONDITIONAL: "CONDITIONAL", // Functions with conditional logic
  COUNT: "COUNT", // Counting functions
  DATEDIFF: "DATEDIFF", // Date difference calculation
};

/**
 * Maps model names to their primary object types
 * Used to identify the correct entity type for extended models
 */
export const MODELMAP = {
  extendedDeal: PRIMARYOBJECT.OPPORTUNITY,
  extendedLead: PRIMARYOBJECT.LEAD,
  extendedAccount: PRIMARYOBJECT.ACCOUNT,
};

/**
 * Commission plan types
 * SPIFF: Special Performance Incentive Funds - one-time bonuses
 * MAIN: Regular ongoing commission plans
 */
export const PLANTYPE = { SPIFF: "SPIFF", MAIN: "MAIN" };

/**
 * Status values for Dispute Resolution System (DRS) tickets
 */
export const DRS_STATUS = {
  ASSIGNED: "Assigned", // Ticket assigned to staff
  OPEN: "Open", // Ticket is open but unassigned
  CLOSED: "Closed", // Ticket has been resolved
};

/**
 * Payout frequency options for commission payments
 */
export const PAYOUT_FREQUENCY = {
  MONTHLY: "Monthly",
  QUARTERLY: "Quarterly",
  HALFYEARLY: "Halfyearly",
  ANNUAL: "Annual",
};

/**
 * Types of line items in commission calculations
 * Maps to primary object types
 */
export const LINE_ITEM_TYPE = {
  Deal: "Opportunity",
  Lead: "Lead",
  Accout: "Account",
  Activity: "Activity",
};

/**
 * Draw types for advance payments
 * NRG: Non-Recoverable Guarantee - cannot be clawed back
 * RG: Recoverable Guarantee - can be recouped from future commissions
 * NR: Non-Recoverable - draw that cannot be clawed back
 * R: Recoverable - draw that will be recouped
 */
export const DRAW_TYPE = {
  NonRecoverableGuarantee: "NRG",
  RecoverableGuarantee: "RG",
  NonRecoverable: "NR",
  Recoverable: "R",
};

/**
 * Human-readable display names for draw types
 */
export const DRAW_TYPE_NAMES = {
  [DRAW_TYPE.NonRecoverableGuarantee]: "Non-recoverable Guarantee",
  [DRAW_TYPE.RecoverableGuarantee]: "Recoverable Guarantee",
  [DRAW_TYPE.NonRecoverable]: "Non-Recoverable",
  [DRAW_TYPE.Recoverable]: "Recoverable",
};

/**
 * Maps application data types to Handsontable data types
 * Used for rendering editable tables
 */
export const HOT_TABLE_DATATYPES = {
  [DATATYPE.INTEGER]: "numeric",
  [DATATYPE.STRING]: "text",
  [DATATYPE.DATE]: "date",
  [DATATYPE.PERCENTAGE]: "numeric",
  [DATATYPE.BOOLEAN]: "dropdown",
};

/**
 * Quota category definitions
 */
export const QUOTA_CATEGORIES = {
  PRIMARY_QUOTA: "Primary",
};

/**
 * Maps quota categories to system names
 */
export const QUOTA_CATEGORY_NAME = {
  [QUOTA_CATEGORIES.PRIMARY_QUOTA]: "PRIMARY_QUOTA",
};

/**
 * Alternative data type names used in different contexts
 * (Simplified version of the main DATATYPE enum)
 */
export const NEW_DATATYPES = {
  INTEGER: "Integer",
  STRING: "String",
  DATE: "Date",
  BOOLEAN: "Boolean",
  PERCENTAGE: "Percentage",
  EMAIL: "Email",
  HIERARCHY: "Hierarchy",
};

/**
 * Possible states for checkbox selection groups
 * Used in multi-select interfaces
 */
export const CHECKBOX_STATUS = {
  ALL: "all", // All items selected
  NONE: "none", // No items selected
  INDETERMINATE: "indeterminate", // Some items selected
};

/**
 * Maps the applicable primary objects to other primary objects
 * Defines valid object relationships (what objects can be linked to what)
 */
export const APPLICABLE_PRIMARY_OBJECTS = {
  [PRIMARYOBJECT.OPPORTUNITY]: [
    PRIMARYOBJECT.OPPORTUNITY,
    PRIMARYOBJECT.LEAD,
    PRIMARYOBJECT.ACTIVITY,
  ],
  [PRIMARYOBJECT.LEAD]: [
    PRIMARYOBJECT.OPPORTUNITY,
    PRIMARYOBJECT.LEAD,
    PRIMARYOBJECT.ACTIVITY,
  ],
  [PRIMARYOBJECT.ACCOUNT]: [
    PRIMARYOBJECT.OPPORTUNITY,
    PRIMARYOBJECT.LEAD,
    PRIMARYOBJECT.ACCOUNT,
    PRIMARYOBJECT.ACTIVITY,
  ],
  Contract: [
    PRIMARYOBJECT.OPPORTUNITY,
    PRIMARYOBJECT.LEAD,
    PRIMARYOBJECT.ACTIVITY,
  ],
  [PRIMARYOBJECT.ACTIVITY]: [PRIMARYOBJECT.ACTIVITY],
};

/**
 * Model constants used for data sources and entity references
 */
export const MODELS = {
  OPPORTUNITY: PRIMARYOBJECT.OPPORTUNITY,
  LEAD: PRIMARYOBJECT.LEAD,
  ACCOUNT: PRIMARYOBJECT.ACCOUNT,
  CONTRACT: "Contract",
  ACTIVITY: PRIMARYOBJECT.ACTIVITY,
  INVOICE: "Invoice",
};

/**
 * Maps payout frequency to number of months in each period
 * Used for calculating proration and date ranges
 */
export const PAYOUT_FREQ_MONTHS = {
  Monthly: 1,
  Quarterly: 3,
  Halfyearly: 6,
  Annual: 12,
};

/**
 * Common UI text constants for consistent messaging
 */
export const UI_TEXT = {
  NA: "NA",
  NOT_AVAILABLE: "Not Available",
};

/**
 * Period types used for time-based calculations
 */
export const PAYOUT_PERIOD = {
  DAY: "Day",
  MONTH: "Month",
  QUARTER: "Quarter",
  HALFYEAR: "Halfyear",
  YEAR: "Year",
};

/**
 * Datasheet source type definitions with human-readable labels
 * Used in the datasheet creation UI
 */
export const DATASHEET_SOURCE_TYPE_CODES = {
  OBJECT: {
    label: "Object",
    value: "custom_object",
  },
  REPORT_OBJECT: {
    label: "Report Object",
    value: "report_object",
  },
  DATASHEET: {
    label: "Datasheet",
    value: "datasheet",
  },
};

/**
 * Source type code constants for datasheet data sources
 */
export const SOURCE_TYPE_CODES = {
  OBJECT: "object",
  REPORT_OBJECT: "report",
  DATASHEET: "datasheet",
};

/**
 * Human-readable names for source type codes
 */
export const SOURCE_TYPE_NAMES = {
  [SOURCE_TYPE_CODES.OBJECT]: "Object",
  [SOURCE_TYPE_CODES.DATASHEET]: "Datasheet",
  [SOURCE_TYPE_CODES.REPORT_OBJECT]: "Report Object",
};

/**
 * Data origin types for reporting and analysis
 * Defines where data was sourced from
 */
export const DATA_ORIGIN = {
  COMMISSION_OBJECT: "commission_object", // Commission calculation data
  SYSTEM_OBJECT: "system_object", // Core system data
  CUSTOM_OBJECT: "custom_object", // User-defined objects
  INTER_OBJECT: "inter_object", // Inter-object relationships
  FORECAST_OBJECT: "forecast_object", // Forecast data
  INTER_FORECAST_OBJECT: "inter_forecast_object", // Inter-forecast relationships
};

/**
 * Transformation codes for datasheet operations
 * Defines the available data transformation types
 */
export const T_CODES = {
  FILTER: "FILTER", // Filter rows based on conditions
  SORT: "SORT", // Sort rows by columns
  UNION: "UNION", // Combine multiple datasets
  JOIN: "JOIN", // Join datasets on keys
  GROUP_BY: "GROUP_BY", // Group and aggregate data
  ADVANCED_FILTER: "ADVANCED_FILTER", // Complex filtering
  ADVANCED_FILTER_V2: "ADVANCED_FILTER_V2", // Enhanced filtering
  TEMPORAL_SPLICE: "TEMPORAL_SPLICE", // Time-based data operations
  FLATTEN: "FLATTEN", // Flatten hierarchical data
  GET_USER_PROPERTIES: "GET_USER_PROPERTIES", // Retrieve user properties
};

/**
 * Human-readable names for transformation codes
 */
export const T_NAMES = {
  [T_CODES.FILTER]: "Basic Filter",
  [T_CODES.SORT]: "Sort",
  [T_CODES.UNION]: "Union",
  [T_CODES.JOIN]: "Join",
  [T_CODES.GROUP_BY]: "Group by",
  [T_CODES.ADVANCED_FILTER]: "Filter",
  [T_CODES.ADVANCED_FILTER_V2]: "Filter",
  [T_CODES.TEMPORAL_SPLICE]: "Temporal Splice",
  [T_CODES.FLATTEN]: "Flatten Hierarchy",
  [T_CODES.GET_USER_PROPERTIES]: "Get User Properties",
};

/**
 * Aggregation functions available in the system
 * Defines how values can be combined in reports and analyses
 */
export const AGG = {
  AVG: {
    code: "AVG",
    name: "AVG",
    dataType: [DATATYPE.INTEGER],
    prefix: "avg_",
    dPrefix: "AVG::",
    outputType: DATATYPE.INTEGER,
  },
  COUNT: {
    code: "COUNT",
    name: "COUNT",
    dataType: null,
    prefix: "count_",
    dPrefix: "COUNT::",
    outputType: DATATYPE.INTEGER,
  },
  COUNT_DISTINCT: {
    code: "COUNT_DISTINCT",
    name: "COUNTDISTINCT",
    dataType: null,
    prefix: "count_distinct_",
    dPrefix: "COUNT DISTINCT::",
    outputType: DATATYPE.INTEGER,
  },
  MIN: {
    code: "MIN",
    name: "MIN",
    dataType: [DATATYPE.INTEGER, DATATYPE.DATE],
    prefix: "min_",
    dPrefix: "MIN::",
    outputType: null,
  },
  MAX: {
    code: "MAX",
    name: "MAX",
    dataType: [DATATYPE.INTEGER, DATATYPE.DATE],
    prefix: "max_",
    dPrefix: "MAX::",
    outputType: null,
  },
  SUM: {
    code: "SUM",
    name: "SUM",
    dataType: [DATATYPE.INTEGER],
    prefix: "sum_",
    dPrefix: "SUM::",
    outputType: DATATYPE.INTEGER,
  },
};

/**
 * Everstage-generated columns that are automatically added to commission data
 * These are special system fields with specific meaning in the application
 */
export const EVERSTAGE_GENERATED_COLUMNS = {
  TIERNAME: {
    name: "Tier / Overridden Tier",
    shortName: "Tier",
    systemName: "tierName",
  },
  ORIGINALTIERNAME: {
    name: "Actual Tier",
    systemName: "originalTierName",
  },
  QUOTAEROSION: {
    name: "Quota Retirement",
    systemName: "quotaErosion",
  },
  COMMISSION: {
    name: "Commissions",
    systemName: "commission",
  },
};

/**
 * Fixed columns that must be present in certain data structures
 */
export const FIXED_COLUMNS = {
  COMMISSION: EVERSTAGE_GENERATED_COLUMNS.COMMISSION.systemName,
};

/**
 * Payment status codes for commission payments
 * Tracks the state of commission payouts
 */
export const PAYMENT_STATUS = {
  PAID: "Paid", // Payment has been made
  UNPAID: "Unpaid", // Payment is due but not yet made
  PARTIALLY_PAID: "Partially Paid", // Only part of amount has been paid
  ZERO_PAYOUT: "Zero Payout", // No payment due (zero amount)
  OVER_PAID: "Over Paid", // More was paid than was due
};

/**
 * Approval status codes for approval workflows
 * Used in statement approval processes
 */
export const APPROVAL_STATUS = {
  APPROVED: "approved", // Request approved
  REJECTED: "rejected", // Request rejected
  WITHDRAWN: "withdrawn", // Request withdrawn by requester
  REQUESTED: "requested", // Approval requested, pending review
  ABORTED: "aborted", // Process automatically canceled
  NOT_REQUESTED: "not_requested", // No approval requested yet
  PENDING: "pending", // Approval is pending
  NEEDS_ATTENTION: "needs_attention", // Requires manual review
  REVOKED: "revoked", // Approval has been revoked
};

/**
 * User-friendly display text for approval statuses in statements
 */
export const APPROVAL_STATUS_STATEMENTS = {
  APPROVED: "Approved",
  PENDING: "Pending approval",
  REJECTED: "Rejected",
  WITHDRAWN: "Withdrawn",
  CANCELLED: "Cancelled",
  REVOKED: "Revoked",
};

/**
 * Approval status codes for line item level approvals
 * Used when individual line items can be approved separately
 */
export const LINE_ITEM_APPROVAL_STATUS = {
  APPROVED: "accepted",
  REJECTED: "declined",
  WITHDRAWN: "withdrawn",
  PENDING: "requested",
  CANCELLED: "aborted",
  REVOKED: "revoked",
};

/**
 * Commission calculation status values
 * Indicates whether commission calculations can be modified
 */
export const CALCULATION_STATUS = {
  FROZEN: "Frozen", // Calculations are locked and cannot be changed
  NOTFROZEN: "Not Frozen", // Calculations can still be modified
};

/**
 * List of month names for date formatting and display
 */
export const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

/**
 * Action types for commission operations
 * Used to identify what operation is being performed
 */
export const COMMISSION_ACTION_STATUS = {
  FREEZE: "freeze", // Lock calculations
  UNFREEZE: "unfreeze", // Unlock calculations
  MAKE_PAYMENT: "makePayment", // Process payments
  COMMISSION: "commissions", // General commission operations
  SETTLEMENT: "settlements", // Settlement operations
  REQUEST_APPROVAL: "requestApproval", // Request approval for payouts
  REVOKE_APPROVAL: "revokeApproval", // Revoke an approval
  EMAIL_STATEMENTS: "emailStatements", // Send statements via email
  DOWNLOAD_STATEMENTS: "downloadStatements", // Download statement documents
};

/**
 * Commission view types for different display modes
 */
export const COMMISSION_VIEW = {
  PAYOUTS: "payouts", // View of commission payouts
  ARREARS: "arrears", // View of late/delayed payments
};

/**
 * Filter configuration types for different filtering approaches
 */
export const FILTER_CONFIG_TYPE = {
  FILTER: "FILTER", // Standard filtering
  PIVOT: "PIVOT", // Pivot table filtering
};

/**
 * Available aggregation types for pivot tables by data type
 * Defines what aggregation operations can be used with each data type
 */
export const PIVOT_AGG = {
  Boolean: [{ label: "Count", value: "count" }],
  Integer: [
    { label: "Sum", value: "sum" },
    { label: "Count", value: "count" },
    { label: "Distinct Count", value: "nunique" },
    { label: "Max", value: "max" },
    { label: "Min", value: "min" },
    { label: "Average", value: "avg" },
  ],
  Percentage: [
    { label: "Sum", value: "sum" },
    { label: "Count", value: "count" },
    { label: "Distinct Count", value: "nunique" },
    { label: "Max", value: "max" },
    { label: "Min", value: "min" },
    { label: "Average", value: "avg" },
  ],
  String: [
    { label: "Count", value: "count" },
    { label: "Distinct Count", value: "nunique" },
  ],
  Date: [
    { label: "Count", value: "count" },
    { label: "Distinct Count", value: "nunique" },
    { label: "Max", value: "max" },
    { label: "Min", value: "min" },
  ],
  Email: [
    { label: "Count", value: "count" },
    { label: "Distinct Count", value: "nunique" },
  ],
};

/**
 * Maps pivot table aggregation codes to human-readable names
 */
export const PIVOT_AGG_MAP = {
  sum: "Sum",
  count: "Count",
  nunique: "Distinct Count",
  max: "Max",
  min: "Min",
  avg: "Average",
};

/**
 * User status values for user account lifecycle states
 */
export const USER_STATUS_MAP = {
  ADDED: "Added", // User has been added to the system
  INVITED: "Invited", // User has been invited but hasn't registered
  ACTIVE: "Active", // User is active and can log in
  INACTIVE: "Inactive", // User is deactivated
  MARKED_FOR_EXIT: "Marked for exit", // User is scheduled for removal
  PENDING_EXIT: "Pending exit", // User is in process of being removed
  MARKED_FOR_DEACTIVATION: "Marked for deactivation", // User to be deactivated
};

/**
 * Payee role filter options for filtering views based on hierarchy
 */
export const PAYEE_ROLE_FILTER = {
  ALL: "all", // Show all payees
  SELF: "self", // Show only the current user
  SELF_AND_REPORTEES: "self_and_reportees", // Show user and direct reports
};

/**
 * Search box configuration constants
 */
export const SEARCH_BOX = {
  DEBOUNCE_TIME: 500, // Time in ms to wait before executing search
  MINIMUM_CHARS: 3, // Minimum characters required before search triggers
};

/**
 * Integration connection status values
 */
export const CONNECTION_STATUS = {
  CONNECTED: "CONNECTED",
  ERROR: "ERROR",
};

export const INTEGRATION = {
  ADP_WORKFORCE: "adp_workforce",
  AIRTABLE: "airtable",
  AWS_S3: "aws_s3",
  BAMBOO_HR: "bamboo_hr",
  BIGQUERY: "bigquery",
  BULLHORN: "bullhorn",
  CERIDIAN_DAYFORCE: "ceridian_dayforce",
  CHARGEBEE: "chargebee",
  CLOSE: "close",
  CSV: "csv",
  EMPLOYMENT_HERO: "employment_hero",
  FRESHSALES: "freshworks",
  GOOGLE_SHEETS: "google_sheets",
  GUSTO: "gusto",
  HUBSPOT: "hubspot",
  MANUAL_UPLOAD: "manual upload",
  MAXIO: "maxio",
  MERGE: "merge",
  MICROSOFT_DYNAMICS_365: "microsoft_dynamics_365",
  MSSQL: "mssql",
  MYSQL: "mysql",
  PERSONIO: "personio",
  PIPEDRIVE: "pipedrive",
  POSTGRESQL: "postgres",
  QUICKBOOKS: "quickbooks",
  REDSHIFT: "redshift",
  S3: "s3",
  SAASOPTICS: "saasoptics",
  GONG: "gong",
  SAGE_INTACCT: "sage_intact",
  SALESFORCE: "salesforce",
  SERVICETITAN: "servicetitan",
  SFTP: "sftp",
  SHAREPOINT: "sharepoint",
  SHOPIFY: "shopify",
  SNOWFLAKE: "snowflake",
  SQL: "sql",
  STRIPE: "stripe",
  SUITEANALYTICS: "suiteanalytics",
  WORKDAY: "workday",
  XERO: "xero",
  ZOHO: "zoho",
};

export const ACTIVITY_LOGS_STATUS = {
  PENDING: "PENDING",
  PROCESSING: "PROCESSING",
  DONE: "DONE",
  FAILED: "FAILED",
  PARTIALLY_FAILED: "PARTIALLYFAILED",
};

export const ACTIVITY_LOGS_JOBS = {
  DATAIMPORT: "DATAIMPORT",
  QUOTAIMPORT: "QUOTAIMPORT",
  USERIMPORT: "USERIMPORT",
  COMMISSIONSYNC: "COMMISSIONSYNC",
  CONTRACTS_STATUS_EXPORT: "CONTRACTS_STATUS_EXPORT",
  BULK_DOWNLOAD_STATEMENTS: "BULK_DOWNLOAD_STATEMENTS",
  BULK_DELETE_USERS: "BULK_DELETE_USERS",
  DATASHEET_EXPORT: "DATASHEET_EXPORT",
};

export const CONNECTORS = {
  OBJECTS: "objects",
  CONNECTIONS: "connections",
  INTEGRATION: "integration",
  OBJECT: "object",
  CONNECTION: "connection",
  OBJECT_BACK: "object-back",
  CREATE_CONNECTION: "create-connection",
  CREATE_CONNECTION_TO_LINK: "create-connection-to-link",
  CREATE_OBJECT_CONNECTION: "create-object-connection",
  CREATE_OBJECT_MANUALLY: "create-object-manually",
  CREATE_CONNECTION_SUCCESS: "create-connection-success",
};

export const CSV_EXPORT_TYPE = {
  FILTERED: "FILTERED",
  ALL: "ALL",
};

export const REQUEST_STATUS = {
  REQUESTED: "requested",
  REJECTED: "rejected",
  APPROVED: "approved",
  WITHDRAWN: "withdrawn",
};

export const APPROVAL_ENTITY_TYPES = {
  PAYOUT: "payout",
  QUOTE: "quote",
  PAYOUT_LINE_ITEM: "payout_line_item",
};

export const HEADER_STATE = {
  EVERYTHING: "EVERYTHING",
  ALL: "ALL",
  NONE: "NONE",
  PARTIAL: "PARTIAL",
};

export const ENTITY_KEY_DELIMITER = "##::##";

export const APPROVAL_STAGE_STATUS = {
  NOTSTARTED: "not_started",
  STARTED: "started",
  REQUESTED: "requested",
  COMPLETED: "completed",
  WITHDRAWN: "withdrawn",
  REJECTED: "rejected",
  ABORTED: "aborted",
  REVOKED: "revoked",
  REVOKED_TITLE: "revoked_title",
};

export const LIST_TYPE = {
  HIERARCHY_TREE: "HierarchyTree",
};

export const QUOTA_SCHEDULE = {
  Monthly: "Monthly",
  Quarterly: "Quarterly",
  Halfyearly: "Half-Yearly",
  Annual: "Annual",
};

export const UNLOCK_STATUS =
  "Any pending approval requests and previous approvals will be cancelled if unlocked.";

export const REQUEST_APPROVAL_SINGLE_WARNING =
  "Cannot send an approval request when payout is unlocked, or when approval status is already Requested or Approved.";

export const REQUEST_APPROVAL_BULK_WARNING =
  'Payouts that have approval status as "Requested", "Approved" and Payouts that are not locked will be skipped while sending this approval request.';

export const APPROVAL_STATUS_OPTIONS = [
  { label: "Approved", value: "approved" },
  { label: "Rejected", value: "rejected" },
  { label: "Requested", value: "requested" },
  { label: "Not Requested", value: "not_requested" },
  { label: "Auto cancelled", value: "aborted" },
  { label: "Withdrawn", value: "withdrawn" },
  { label: "Revoked", value: "revoked" },
  { label: "Needs Attention", value: "needs_attention" },
];

export const STAGE_STATUS_OPTIONS = [
  { label: "On track", value: "on_track" },
  { label: "Overdue", value: "overdue" },
];

export const DATASHEET_VIEW_ID = {
  ALL_DATA: "all_data",
  ALL_ADJUSTMENTS: "all_adjustments",
};

export const RBAC_ROLES = {
  VIEW_DASHBOARD: "view:dashboard",
  MANAGE_DASHBOARD: "manage:dashboard",
  DELETE_DASHBOARD: "delete:dashboard",
  VIEW_ADMINDASHBOARD: "view:admindashboard",
  VIEW_PAYEEDASHBOARD: "view:payeedashboard",
  VIEW_DATABOOK: "view:databook",
  MANAGE_DATABOOK: "manage:databook",
  MANAGE_DATASHEETPERMISSIONS: "manage:datasheetpermissions",
  MANAGE_DATASHEETADJUSTMENTS: "manage:datasheetadjustments",
  EXPORT_DATASHEET: "export:datasheet",
  DELETE_DATASHEET: "delete:datasheet",
  VIEW_TERRITORY_PLANS: "view:territoryplans",
  EXPLORE_TERRITORY_PLANS: "explore:territoryplans",
  VIEW_COMMISSIONPLAN: "view:commissionplan",
  EDIT_COMMISSIONPLAN: "edit:commissionplan",
  CREATE_COMMISSIONPLAN: "create:commissionplan",
  DELETE_COMMISSIONPLAN: "delete:commissionplan",
  VIEW_COMMISSIONFEED: "view:commissionfeed",
  MANAGE_COMMISSIONFEED: "manage:commissionfeed",
  VIEW_PAYOUTS: "view:payouts",
  MANAGE_PAYOUTS: "manage:payouts",
  REGISTER_PAYOUTS: "register:payouts",
  INVALIDATE_PAYOUTS: "invalidate:payouts",
  VIEW_REQUESTAPPROVALS: "view:requestapprovals",
  EXPORT_PAYOUTS: "export:payouts",
  VIEW_STATEMENTS: "view:statements",
  EXPORT_STATEMENT: "export:statement",
  VIEW_HIDDENCRITERIA: "view:hiddencriteria",
  MANAGE_COMMISSIONADJUSTMENT: "manage:commissionadjustment",
  VIEW_PAYOUTVALUEOTHERS: "view:payoutvalueothers",
  MANAGE_CRYSTAL: "manage:crystal",
  VIEW_QUOTAS: "view:quotas",
  MANAGE_QUOTAS: "manage:quotas",
  MANAGE_QUOTA_SETTINGS: "manage:quotasettings",
  VIEW_HIDDENQUOTAS: "view:hiddenquotas",
  VIEW_DRAWS: "view:draws",
  MANAGE_DRAWS: "manage:draws",
  MANAGE_USERS: "manage:users",
  VIEW_USERS: "view:users",
  DELETE_USERS: "delete:users",
  VIEW_PAYROLL: "view:payroll",
  EDIT_PAYROLL: "edit:payroll",
  EXPORT_USERS: "export:users",
  ALLOW_IMPERSONATION: "allow:impersonation",
  VIEW_TEAMS: "view:teams",
  MANAGE_USERGROUPS: "manage:usergroups",
  MANAGE_USERCUSTOMFIELD: "manage:usercustomfield",
  MANAGE_PAYROLL_ENTRIES_USER: "manage:payrollentriesusers",
  VIEW_QUERIES: "view:queries",
  CREATE_QUERIES: "create:queries",
  EDIT_QUERIES: "edit:queries",
  DELETE_QUERIES: "delete:queries",
  MANAGE_CONFIG: "manage:config",
  MANAGE_CONTRACTS: "manage:contracts",
  MANAGE_DATASETTINGS: "manage:datasettings",
  MANAGE_ROLES: "manage:roles",
  MANAGE_OWNDATA: "manage:owndata",
  MANAGE_REPORTENRICH: "manage:reportenrich",
  MANAGE_ALL_ADMINS: "manage:alladmins",
  MANAGE_ANALYTICS: "manage:analytics",
  MANAGE_ACCOUNTNOTIFICATIONS: "manage:accountnotifications",
  VIEW_EVERSTAGE: "view:everstage",
  VIEW_APPROVALS: "view:approvals",
  MANAGE_AGENT_STUDIO: "manage:agentstudio",
  MANAGE_AGENT_WORKBENCH: "manage:agentworkbench",
  MANAGE_AUTOGEN_DESCRIPTION: "manage:autogendescription",
  MANAGE_COMMISSION_FORMULA_GENERATION: "manage:commissionformulageneration",
  MANAGE_DATASHEET_AI_GENERATION: "manage:datasheetaigeneration",
  MANAGE_APPROVAL_WORKFLOWS: "manage:approvalworkflows",
  PUBLISH_COMMISSION_PLAN: "publish:commissionplan",
  ACCESS_HELP_CENTRE: "access:helpcenter",
  MANAGE_MULTI_PERIOD_SYNC: "manage:multiperiodsync",
  VIEW_GLOBAL_SEARCH: "view:globalsearch",
};

/**
 * URL paths for dashboard pages
 */
export const DASHBOARD_URLS = {
  DASHBOARDS: "/dashboards",
  DASHBOARD: "/dashboard",
};

/**
 * Dashboard type classifications
 */
export const DASHBOARD_TYPES = {
  ADMIN_DASHBOARD: "admin_dashboard", // Dashboard for administrators
  PAYEE_DASHBOARD: "payee_dashboard", // Dashboard for commission payees
  SUPERSET_DASHBOARD: "superset_dashboard", // Apache Superset dashboard integration
};

/**
 * Default dashboard names
 */
export const DASHBOARD_PAGE_NAMES = {
  DEFAULT_DASHBOARD: "Default Dashboard",
  SUPERSET_DASHBOARD: "Superset Dashboard",
};

/**
 * Default dashboard ID values
 */
export const DEFAULT_DASHBOARD_ID = {
  ADMIN_DASHBOARD: "1",
  PAYEE_DASHBOARD: "2",
};

/**
 * URL paths for databook pages
 */
export const DATABOOK_URLS = {
  DATABOOKS: "/databook",
};

/**
 * URL paths for CPQ (Configure, Price, Quote) module
 */
export const CPQ_URLS = {
  QUOTES: "/cpq/quotes",
};

/**
 * Page names for CPQ module navigation
 */
export const CPQ_PAGE_NAMES = {
  DATABOOK: "Databook",
  DATASHEET: "Datasheet",
  DATASHEETS: "Datasheets",
  QUOTES: "Quotes",
  CATALOG: "Catalog",
  PRODUCT: "Product",
  PRICE_BOOK: "PriceBook",
  USERS: "Users",
  USER_GROUPS: "Groups",
  SETTINGS: "Settings",
  EVERAI: "EverAI",
};

/**
 * Page names for the main Everstage sidebar navigation
 * These define the labels shown in the navigation menu
 */
export const EVERSIDER_PAGE_NAMES = {
  DASHBOARD: "Dashboards",
  DATABOOK: "Databook",
  DATASHEET: "Datasheet",
  DATASHEETS: "Datasheets",
  OBJECTS: "Objects",
  TERRITORY_PLANS: "Territory Plans",
  STATEMENTS: "Statements",
  COMMISSION_PLAN: "Plans",
  FORECAST: "Forecasts",
  COMMISSION_FEED: "Feed",
  COMMISSION_PAYOUTS: "Payouts",
  APPROVALS: "Approvals",
  PAYOUT_APPROVALS: "Payout",
  COMMISSION_ADJUSTMENT_APPROVALS: "Commission",
  CRYSTAL: "Crystal",
  USERS: "Users",
  USER_GROUPS: "Groups",
  TEAMS: "Teams",
  QUOTAS: "Quotas",
  DRAWS: "Draws",
  QUERIES: "Queries",
  CONTRACTS: "Contracts",
  SETTINGS: "Settings",
  METRICS: "Metrics",
  EXPLORER: "Explorer",
  SEARCH: "Search",
  TRANSFORMATION: "Transformation",
  KPI: "KPI",
  EVERAI: "EverAI",
  ...CPQ_PAGE_NAMES,
};

/**
 * Constant for invalid date representation
 */
export const INVALID_DATE = "Invalid date";

/**
 * Constants for supabase realtime database operations
 * Used for tracking and notifying about background tasks
 */
export const SUPABASE_CONSTANTS = {
  // Approval instance creation tasks
  BULK_CREATE_APPROVAL_INSTANCES: {
    TASK_NAME: "BULK_CREATE_APPROVAL_INSTANCES",
    CHANNEL_NAME: "create-bulk-approval-instance",
    EVENT_TYPE: ["INSERT", "UPDATE"],
    PENDING: "PENDING",
    PROCESSING: "PROCESSING",
    DONE: "DONE",
    FAILED: "FAILED",
  },
  // Approval revocation tasks
  BULK_REVOKE_APPROVAL_INSTANCES: {
    TASK_NAME: "BULK_REVOKE_APPROVAL_INSTANCES",
    CHANNEL_NAME: "bulk-revoke-approval-instances",
    EVENT_TYPE: ["INSERT", "UPDATE"],
    PENDING: "PENDING",
    PROCESSING: "PROCESSING",
    DONE: "DONE",
    FAILED: "FAILED",
  },
  // Email statement tasks
  EMAIL_STATEMENTS: {
    TASK_NAME: "BULK_EMAIL_STATEMENTS",
    CHANNEL_NAME: "email-statements",
    EVENT_TYPE: { UPDATE: "UPDATE", INSERT: "INSERT" },
    PENDING: "PENDING",
    PROCESSING: "PROCESSING",
    DONE: "DONE",
    FAILED: "FAILED",
  },
  // Data import tasks
  BULK_IMPORT_DATA: {
    TASK_NAME: "BULK_IMPORT_DATA",
    CHANNEL_NAME: "bulk-import-data",
    EVENT_TYPE: ["INSERT", "UPDATE"],
  },
  // HRIS sync tasks
  HRIS_SYNC: {
    TASK_NAME: "UPSTREAM_SYNC",
    CHANNEL_NAME: "hris-sync",
    EVENT_TYPE: ["INSERT", "UPDATE"],
    STARTED: "STARTED",
    FAILED: "FAILED",
    COMPLETED: "COMPLETE",
  },
  // Upstream sync tasks
  UPSTREAM_SYNC: {
    TASK_NAME: "UPSTREAM_SYNC",
    CHANNEL_NAME: "upstream-sync",
    EVENT_TYPE: ["INSERT", "UPDATE"],
  },
  // HRIS data import tasks
  HRIS_IMPORT_RECORDS: {
    TASK_NAME: "IMPORT_HRIS_DATA",
    CHANNEL_NAME: "import-hris-data",
    EVENT_TYPE: ["INSERT", "UPDATE"],
    STARTED: "started",
    FAILED: "FAILED",
    COMPLETED: "DONE",
  },
  // Statement download tasks
  BULK_DOWNLOAD_STATEMENTS: {
    TASK_NAME: "BULK_DOWNLOAD_STATEMENTS",
    CHANNEL_NAME: "bulk-download-statements",
    EVENT_TYPE: ["INSERT", "UPDATE"],
    PROCESSING: "PROCESSING",
    PENDING: "PENDING",
    DONE: "DONE",
    FAILED: "FAILED",
  },
  // EverAI message tasks
  EVERAI_MESSAGE: {
    TASK_NAME: "EVERAI_MESSAGE",
    CHANNEL_NAME: "everai-message",
    EVENT_TYPE: ["INSERT", "UPDATE"],
  },
  // Datasheet export tasks
  DATASHEET_EXPORT_V2: {
    TASK_NAME: "DATASHEET_EXPORT_V2",
    CHANNEL_NAME: "datasheet-export-v2",
    EVENT_TYPE: ["INSERT", "UPDATE"],
    PENDING: "PENDING",
    PROCESSING: "PROCESSING",
    DONE: "DONE",
    FAILED: "FAILED",
  },
};

/**
 * Special key for roles that applies to all roles
 */
export const ALL_ROLES_KEY = "ALL";

/**
 * Mapping status values for payee-to-plan mappings
 */
export const MAPPING_STATUS = {
  MAPPED: "Mapped", // Payee is mapped to a plan
  UNMAPPED: "Unmapped", // Payee is not mapped to a plan
};

/**
 * Component identifiers for system modules
 * Used for permission management and navigation
 */
export const COMPONENTS = {
  DASHBOARD: "dashboard",
  DATABOOKS: "databooks",
  COMMISSION_PLANS: "commission_plans",
  COMMISSION_FEED: "commission_feed",
  PAYOUTS_STATEMENTS: "payouts_statements",
  CRYSTAL: "crystal",
  TERRITORY_PLANS: "territory_plans",
  QUOTAS_DRAWS: "quotas_draws",
  MANAGE_USERS: "manage_users",
  QUERIES: "queries",
  SETTINGS: "settings",
  ADVANCED_PERMISSION: "advanced_permissions",
  ALL_DATA: "all_data",
};

/**
 * Data permission access level types
 * Defines what data a user can access based on role
 */
export const DATA_PERMISSION_TYPES = {
  INDIVIDUAL_DATA: "INDIVIDUAL_DATA", // Only user's own data
  INDIVIDUAL_AND_TEAM_DATA: "INDIVIDUAL_AND_TEAM_DATA", // User's data and their team's data
  ALL_DATA: "ALL_DATA", // All data in the system
};

/**
 * Analytics event tracking constants
 * Used to track user actions for analytics purposes
 */
export const ANALYTICS_EVENTS = Object.freeze({
  // Help and support events
  VISIT_HELP_CENTER: "VISIT_HELP_CENTER",
  VISIT_RAISE_QUERY: "VISIT_RAISE_QUERY",
  VISIT_CONTACT_EVERSTAGE: "VISIT_CONTACT_EVERSTAGE",
  VISIT_SOLUTIONS_CORNER: "VISIT_SOLUTIONS_CORNER",

  // Navigation and view selection events
  SWITCH_PERIOD: "SWITCH_PERIOD",
  SWITCH_DISPLAY_CURRENCY: "SWITCH_DISPLAY_CURRENCY",
  SWITCH_PAYEES: "SWITCH_PAYEES",
  RAISE_QUERY_FROM_STATEMENTS: "RAISE_QUERY_FROM_STATEMENTS",
  VISIT_PAGE: "VISIT_PAGE",

  // Profile and plan viewing events
  VIEW_PROFILE_CARD: "VIEW_PROFILE_CARD",
  VIEW_PLAN_DETAILS: "VIEW_PLAN_DETAILS",
  VIEW_CRITERIA_TABLE_VIEW: "VIEW_CRITERIA_TABLE_VIEW",

  // Quota management events
  SWITCH_QUOTA_CATEGORIES: "SWITCH_QUOTA_CATEGORIES",
  SWITCH_FISCAL_YEAR: "SWITCH_FISCAL_YEAR",
  VIEW_QUOTA: "VIEW_QUOTA",

  // User interface events
  VIEW_LEADERBOARD: "VIEW_LEADERBOARD",
  QUICK_FILTER: "QUICK_FILTER",

  // User management events
  USER_EXPORT: "USER_EXPORT",
  IMPORT_NEW_USERS: "IMPORT_NEW_USERS",
  EDIT_EXISTING_USERS: "EDIT_EXISTING_USERS",
  FILTER_PARAM: "FILTER_PARAM",
  LOGIN_AS_USER: "LOGIN_AS_USER",

  // Search and export events
  SEARCH_USAGE: "SEARCH_USAGE",
  DOWNLOAD_TEMPLATE: "DOWNLOAD_TEMPLATE",
  EXPORTS_CSV: "EXPORTS_CSV",

  // Commission view events
  ARREARS_VIEW: "ARREARS_VIEW",

  // Payee management events
  MANAGE_PAYEE: "MANAGE_PAYEE",
  REMOVE_PAYEE: "REMOVE_PAYEE",
  PREVIEW_AS_A_PAYEE: "PREVIEW_AS_A_PAYEE",
  SHOWING_FOR: "SHOWING_FOR",

  // Filter and statement events
  VIEW_FILTER: "VIEW_FILTER",
  PAYEE_STATEMENT: "PAYEE_STATEMENT",

  // Integration events
  CONNECT_MICROSOFT_TEAMS: "CONNECT_MICROSOFT_TEAMS",
  DISCONNECT_MICROSOFT_TEAMS: "DISCONNECT_MICROSOFT_TEAMS",

  // Report enrichment events
  REPORT_ENRICHMENT: "REPORT_ENRICHMENT",
  DELETE_REPORT_ENRICHMENT: "DELETE_REPORT_ENRICHMENT",
  BULK_DELETE_REPORT_ENRICHMENT: "BULK_DELETE_REPORT_ENRICHMENT",

  // Query and data management events
  QUERY_ASSIGNMENT: "QUERY_ASSIGNMENT",
  GET_DATA_FROM: "GET_DATA_FROM",
  ADD_TRANSFORMATION: "ADD_TRANSFORMATION",
  ADD_FORMULA_FIELD: "ADD_FORMULA_FIELD",
  EDIT_VARIABLE_FIELD: "EDIT_VARIABLE_FIELD",

  // Data export and display events
  EXPORT_CSV: "EXPORT_CSV",
  CUSTOMIZE_COLUMNS: "CUSTOMIZE_COLUMNS",
  PIVOT_MODE: "PIVOT_MODE",
  ADJUSTMENT_TYPE: "ADJUSTMENT_TYPE",

  // Metrics and analytics events
  COUNT_OF_SAVED_VIEWS_PER_SHEET: "COUNT_OF_SAVED_VIEWS_PER_SHEET",
  GET_JOIN_TYPE: "GET_JOIN_TYPE",
  GET_AGGREGATION_TYPE: "GET_AGGREGATION_TYPE",
  COUNT_OF_TRANSFORMATIONS: "COUNT_OF_TRANSFORMATIONS",
  COUNT_OF_FIELDS: "COUNT_OF_FIELDS",
  COUNT_OF_FORMULA_FIELDS: "COUNT_OF_FORMULA_FIELDS",

  // Filter and search events
  APPLY_FILTER: "APPLY_FILTER",
  SEARCH_PAYOUTS: "SEARCH_PAYOUTS",
  PAGINATION: "PAGINATION",

  // View management events
  CREATE_VIEW: "CREATE_VIEW",
  RENAME_DATASHEET_COLUMN_HYPERLINK_CLICKED:
    "RENAME_DATASHEET_COLUMN_HYPERLINK_CLICKED",
});

/**
 * Analytics property names for event tracking
 * These define the metadata that can be attached to analytics events
 */
export const ANALYTICS_PROPERTIES = Object.freeze({
  // Entry point and navigation properties
  ENTRY_POINT: "entry_point",
  PAYEE_NAME: "payee_name",

  // Currency-related properties
  CURRENCY_TYPE: "currency_type",
  CURRENCY_NAME: "currency_name",

  // Time period and page properties
  PERIOD: "period",
  PAGE_NAME: "page_name",

  // Commission plan properties
  CRITERIA_NAME: "criteria_name",
  QUOTA_CATEGORY: "quota_category",
  FISAL_YEAR: "fiscal_year",

  // Dashboard and widget properties
  WIDGET_TYPE_NAME: "widget_type_name",
  DASHBOARD_NAME: "dashboard_name",

  // Filter and export properties
  FILTER_COLUMNS: "filter_columns",
  QUICK_FILTER_TYPE: "quick_filter_type",
  EXPORT_OPTION: "export_option",

  // User management properties
  COUNT_OF_USERS_IMPORTED: "count_of_users_imported",
  LIST_OF_FILTERS: "list_of_filters",
  USER_NAME: "user_name",
  USER_ROLE: "user_role",

  // Search and download properties
  TEXT_SEARCHED: "text_searched",
  TEMPLATE_DOWNLOAD: "template_download",
  TIME_TO_EXPORT: "time_to_export",

  // View mode properties
  IS_ARREARS_VIEW: "is_arrears_view",

  // Crystal (forecasting) properties
  CRYSTAL_SIMULATOR_NAME: "crystal_simulator_name",
  ADDL_PAYEES_TO_CRYSTAL_VIEW: "addl_payees_to_crystal_view",
  REMOVE_PAYEE: "remove_payee",
  CRYSTAL_VIEW_NAME: "crystal_view_name",

  // Value management properties
  OVERWRITE_VALUES: "overwrite_values",
  TIME_IN_FILTER_PAGE: "time_in_filter_page",
  VIEW_PAYOUTS_FILTER: "view_payouts_filter",

  // User identification properties
  ADMIN_EMAIL: "admin_email",

  // Integration properties
  CONNECT_MS_TEAMS_DATE: "connect_ms_teams_date",
  DISCONNECT_MS_TEAMS: "disconnect_ms_teams",
  DISCONNECT_MS_TEAMS_DATE: "disconnect_ms_teams_date",

  // Commission plan properties
  COMMISSION_PLAN_NAME: "commission_plan_name",
  CRITERIA: "criteria",

  // Databook and datasheet properties
  DATABOOK: "databook",
  DATASHEET: "datasheet",
  REPORT_TYPE: "report_type",
  COUNT_OF_VARIABLES_ADDED: "count_of_variables_added",
  INCLUDE_ALL_PLANS_USING_THIS_DS: "include_all_plans_using_this_ds",

  // Query properties
  QUERY_ASSIGNMENT_OPTION: "query_assignment_option",
  ALLOW_CC: "allow_cc",

  // Data operation properties
  DATASHEET_NAME: "datasheet_name",
  DATA_OPTION: "data_option",
  TRANSFORMATION_OPTION: "transformation_option",
  DATA_TYPE: "data_type",
  FORMULA_FIELD_NAME: "formula_field_name",

  // Export properties
  EXPORT_TYPE: "export_type",

  // Pivot table properties
  COUNT_OF_ROW_GROUPS: "count_of_row_groups",
  COUNT_OF_COLUMN_LABELS: "count_of_column_labels",
  COUNT_OF_VALUES: "count_of_values",

  // Adjustment properties
  ADJUSTMENT_TYPE: "adjustment_type",
  IS_GLOBAL_ADJUSTMENT: "is_global_adjustment",

  // View and transformation properties
  COUNT_OF_SAVED_VIEWS: "count_of_saved_views",
  JOIN_TYPE: "join_type",
  AGGREGATION_TYPE: "aggregation_type",
  COUNT_OF_TRANSFORMATIONS: "count_of_transformations",
  COUNT_OF_FIELDS: "count_of_fields",

  // Filter properties
  APPLY_FILTER: "apply_filter",
  SEARCH_TEXT: "search_text",
  QUICK_FILTER: "quick_filter",
  PAGINATED_VALUE: "paginated_value",

  // Platform properties
  PLATFORM_USED: "platform_used",

  // Display condition properties
  DISPLAY_CONDITION_FIELD: "display_condition_field",
  COUNT_OF_DISPLAY_CONDITIONS: "count_of_display_conditions",

  // Action properties
  SUCCESS_ACTION_FIELD: "success_action_field",
  COUNT_OF_SUCCESS_ACTION: "count_of_success_action",

  // Field properties
  DATE_FIELD_NAME: "date_field_name",
  ROW_NAME_FIELD: "row_name_field",
  COUNT_OF_COLUMNS: "count_of_columns",
  COUNT_OF_EDITABLE_COLUMN_NAMES: "count_of_editable_column_names",

  // Date properties
  DATE_OF_CREATION: "date_of_creation",

  // View properties
  VIEW_NAME: "view_name",

  // User update properties
  COUNT_OF_USERS_UPDATED: "count_of_users_updated",

  // Report properties
  ENRICHMENT_RECORDS: "enrichment_records",

  // Location properties
  LOCATION: "location",
});

/**
 * Platform types for analytics tracking context
 */
export const PLATFORM = {
  USER: "user",
  PAYOUTS: "payouts",
  DATABOOK: "databook",
};

/**
 * Maps URL paths to page names for analytics tracking
 */
export const ANALYTICS_PAGE_PATHS = {
  "/statements": "Statements",
  "/commissions": "Payouts",
  "/crystal": "Crystal",
  "/quotas": "Quotas",
  "/draws": "Draws",
  "/queries": "Queries",
  "/settings/notifications": "Notification settings",
  "/profile-settings": "Profile Settings",
  "/settings/workflows": "Approval Workflows",
  "/approvals/payouts": "Payout Approvals",
  "/approvals/commission-adjustments": "Commission Adjustment Approvals",
};

/**
 * Standard date format patterns
 */
export const DATE_FORMATS = {
  DDMMMYYYY: "DD MMM YYYY",
  DDMMMYYYY_datefns: "dd MMM yyyy",
  MMMYYYY: "MMM YYYY",
};

/**
 * Commission plan status options
 */
export const PLAN_STATUS = {
  ALL: "All", // Show all plans regardless of status
  PUBLISHED: "Published", // Show only published plans
  DRAFT: "Draft", // Show only draft plans
};

/**
 * Sort options for various lists
 */
export const SORT_BY = [
  { label: "Ascending (A-Z)", value: "asc" },
  { label: "Descending (Z-A)", value: "desc" },
  { label: "Last modified", value: "sortByModifiedDate" },
  { label: "Created at", value: "sortByCreateDate" },
  { label: "Plan Start Date", value: "sortByPlanStartDate" },
];

export const FUNCTION_NAMES = {
  SUM: "SUM",
  MIN: "MIN",
  MAX: "MAX",
  AVG: "AVG",
  SUM_IF: "SUMIF",
  COUNT_IF: "COUNTIF",
  COUNT_NOT_NULL: "CountNotNull",
  DISTINCT_COUNT: "DistinctCount",
  IS_EMPTY: "IsEmpty",
  IS_NOT_EMPTY: "IsNotEmpty",
  LOWER: "Lower",
  CONCAT: "Concat",
  COALESCE: "Coalesce",
  FIND: "Find",
  CURRENT_PAYOUT_PERIOD: "CurrentPayoutPeriod",
  CONTAINS: "Contains",
  NOT_CONTAINS: "NotContains",
  DATE_DIFF: "DATEDIFF",
  GETDATE: "GetDate",
  CONFIG: "Config",
  QUOTA_ATTAINMENT: "QuotaAttainment",
  QUOTA_EROSION: "QuotaErosion",
  QUOTA: "Quota",
  TIERED_VALUE: "TieredValue",
  TIERED_PERCENTAGE: "TieredPercentage",
  TEAM_SUM: "TEAM-SUM",
  TEAM_COUNT_NOT_NULL: "TEAM-COUNT-NOT-NULL",
  TEAM_DISTINCT_COUNT: "TEAM-DISTINCT-COUNT",
  TEAM_MIN: "TEAM-MIN",
  TEAM_MAX: "TEAM-MAX",
  TEAM_AVG: "TEAM-AVG",
  TEAM_SUM_IF: "TEAM-SUMIF",
  TEAM_COUNT_IF: "TEAM-COUNTIF",
  TEAM_QUOTA_ATTAINMENT: "TEAM-QuotaAttainment",
  TEAM_QUOTA_EROSION: "TEAM-QuotaErosion",
  TEAM_QUOTA: "TEAM-Quota",
  START_DATE: "StartDate",
  LAST_DATE: "LastDate",
  DATE_ADD: "DateAdd",
  TIMEZONE: "Timezone",
  ROUND: "Round",
  ROUND_UP: "RoundUp",
  ROUND_DOWN: "RoundDown",
  RANK: "Rank",
  TRIM: "Trim",
  LEN: "Len",
  LEFT: "Left",
  RIGHT: "Right",
  MID: "Mid",
  ROLLING_SUM: "RollingSum",
  GET_USER_PROPERTY: "GetUserProperty",
};

export const PROFILE_MODALS = {
  UPLOAD: "upload",
  GENERATE: "generate",
};

export const DOCUMENT_VIEW_SOURCE = "profileDrawer";

export const WINDOW_CALCULATED_FIELDS = ["rank", "rolling", "hierarchy"];

export const DATA_TYPES_TO_BE_IGNORED_IN_MODULE = {
  DATASHEET_ADJUSTMENT: [NEW_DATATYPES.HIERARCHY],
  REPORT_ENRICHMENT: [NEW_DATATYPES.HIERARCHY],
  CRYSTAL: [NEW_DATATYPES.HIERARCHY],
  CRITERIA_DESIGNER: [NEW_DATATYPES.HIERARCHY],
};

export const CUSTOM_TERMINOLOGY_DEFAULT_TERMS_LABELS = {
  QUOTA: "Quota",
  QUOTAS: "Quotas",
  QUOTA_EROSION: "Quota Erosion",
  COMMISSION: "Commission",
  COMMISSIONS: "Commissions",
  PAYOUT: "Payout",
  PAYOUTS: "Payouts",
  ADJUSTMENT: "Adjustment",
  ADJUSTMENTS: "Adjustments",
  ARREARS: "Arrears",
  ARREAR: "Arrear",
  DEFERRED: "Deferred",
  EARNED: "Earned",
  ON_TARGET_VARIABLE_PAY: "On-Target Variable Pay",
};

export const HIERARCHY_CURRENT_SHEET_KEY = "CURRENT_SHEET";
export const CURRENT_DATE_KEY = "CURRENT_DATE";
export const FOR_WHOM_THE_COMMISSION_IS_CALCULATED_KEY =
  "FOR_WHOM_THE_COMMISSION_IS_CALCULATED";

// Values that should be displayed in the UI to denote FOR_WHOM_THE_COMMISSION_IS_CALCULATED and CURRENT_DATE
// When used inside the GetUserProperty commission plan function
export const FOR_WHOM_THE_COMMISSION_IS_CALCULATED_DISPLAY =
  "For whom the commission is calculated";
export const CURRENT_DATE_DISPLAY = "Current date";
export const DATE_PLACEHOLDER = "MMM DD, YYYY";
export const PAYOUT_APPROVAL_TYPES = {
  STATEMENT_LEVEL: "statementLevel",
  LINE_ITEM_LEVEL: "lineItemLevel",
};

export const LINE_ITEM_TYPE_ENTITY_TYPE = "payout_line_item";
export const DATASHEET_VIEW_BY = {
  DATABOOKS: "databooks",
  COMMISSION_PLAN: "commission_plans",
  ARCHIVED_DATABOOKS: "archived_databooks",
  SEPERATOR: "seprator",
};
export const EXPRESSION_TOKEN_TYPES = {
  DATASHEET_VARIABLES: "DATASHEET_VARIABLES",
  DATASHEET_INTER_VARIABLE: "DATASHEET_INTER_VARIABLE",
  CONSTANTS: "CONSTANT_VARIABLES",
  ARRAY: "ARRAY",
  FUNCTIONS: "FUNCTIONS",
  OPERATORS: "OPERATORS",
  BRACKETS: "GROUPING_OPERATORS",
  CLIENT_VARIABLES: "CLIENT_VARIABLES",
  CONFIG_VARIABLES: "CONFIG_VARIABLES",
  QUOTE_VARIABLES: "FORM_VARIABLES",
  LEFT_BRACKET: "(",
  RIGHT_BRACKET: ")",
  USER_DEFINED_FUNCTIONS: "USER_DEFINED_FUNCTIONS",
};

export const EXPRESSION_FUNCTION_TYPES = {
  TieredValue: "TieredValue",
  TieredPercentage: "TieredPercentage",
  SUM: "SUM",
  MIN: "MIN",
  MAX: "MAX",
  AVG: "AVG",
  DISTINCT_COUNT: "DistinctCount",
  Config: "Config",
  QuotaAttainment: "QuotaAttainment",
  QuotaErosion: "QuotaErosion",
  Quota: "Quota",
  DATEDIFF: "DATEDIFF",
  SUMIF: "SUMIF",
  COUNTIF: "COUNTIF",
  IsEmpty: "IsEmpty",
  IsNotEmpty: "IsNotEmpty",
  Contains: "Contains",
  NotContains: "NotContains",
  Lower: "Lower",
  Find: "Find",
  Round: "Round",
  RoundUp: "RoundUp",
  RoundDown: "RoundDown",
  GET_DATE: "GetDate",
  CurrentPayoutPeriod: "CurrentPayoutPeriod",
  CountNotNull: "CountNotNull",
  Concat: "Concat",
  Coalesce: "Coalesce",
  Rank: "Rank",
  Rolling: "Rolling",
  StartDate: "StartDate",
  LastDate: "LastDate",
  DateAdd: "DateAdd",
  Trim: "Trim",
  Len: "Len",
  Left: "Left",
  Right: "Right",
  Mid: "Mid",
  Timezone: "Timezone",
  GetValueFromHierarchy: "GetNthLevelNode",
  Hierarchy: "Hierarchy",
  GetUserProperty: "GetUserProperty",
  StartsWith: "StartsWith",
  EndsWith: "EndsWith",
  DateIsIn: "DateIsIn",

  ["TEAM-SUM"]: "TEAM-SUM",
  ["TEAM-MIN"]: "TEAM-MIN",
  ["TEAM-MAX"]: "TEAM-MAX",
  ["TEAM-AVG"]: "TEAM-AVG",
  ["TEAM-DISTINCT-COUNT"]: "TEAM-DISTINCT-COUNT",
  ["TEAM-COUNT-NOT-NULL"]: "TEAM-COUNT-NOT-NULL",
  ["TEAM-QuotaAttainment"]: "TEAM-QuotaAttainment",
  ["TEAM-QuotaErosion"]: "TEAM-QuotaErosion",
  ["TEAM-Quota"]: "TEAM-Quota",
  ["TEAM-SUMIF"]: "TEAM-SUMIF",
  ["TEAM-COUNTIF"]: "TEAM-COUNTIF",
  ["TEAM-COUNT"]: "TEAM-COUNT",
};

export const EXPRESSION_BOX_STATUS = {
  LOADING: "LOADING",
  WARNING: "WARNING",
  INVALID: "INVALID",
  INITIAL: "INITIAL",
  VALID: "VALID",
  ABORTED: "ABORTED",
};

export const LEARN_MORE_FOR_EXPRESSION_BOX_FUNCTIONS = "LEARN_MORE";

export const COMMISSION_TYPE = {
  COMMISSION_PLAN: "commissionplan",
  FORECAST_PLAN: "forecastplan",
};

export const LOGIN_METHOD = {
  GOOGLE: "GOOGLE",
  SALESFORCE: "SALESFORCE",
};

export const LOGIN_METHOD_TO_AUTH0_CONNECTION = {
  GOOGLE: "google-oauth2",
  SALESFORCE: "salesforce",
};

export const TRIGGER_TYPES = {
  DATASHEET: "datasheet",
  DATASHEET_DATA: "datasheet_data",
  COMMISSION: "commission",
};

export const DATABOOK_TRIGGERS = {
  NEW: "new",
  UPDATED: "updated",
  DELETED: "deleted",
};

export const TRIGGER_CATEGORIES = {
  event_based: {
    label: "Event Based Trigger",
    categories: ["trigger"],
    icon: <NavigationPointer01Icon className="w-4 h-4" />,
  },
  scheduled: {
    label: "Scheduled Trigger",
    categories: ["frequency"],
    icon: <ClockRefreshIcon className="w-4 h-4" />,
  },
  on_demand: {
    label: "On demand Trigger",
    categories: [],
    icon: <ActivityIcon className="w-4 h-4" />,
  },
};

export const STATEMENT_EXPORT_TYPES = {
  PDF: "pdf",
  XLSX: "xlsx",
};

export const COMMISSION_PLAN_WORKFLOW_STATUS = {
  UNDER_REVIEW: "Under review",
  PUBLISHED: "published",
  REJECTED: "rejected",
  REVOKED: "revoked",
  APPROVED: "approved",
  REQUESTED: "requested",
  NOT_REQUESTED: "not_requested",
};
