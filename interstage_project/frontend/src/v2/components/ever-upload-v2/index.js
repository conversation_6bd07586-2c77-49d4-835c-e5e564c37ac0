import { useQuery, gql } from "@apollo/client";
import { RefreshIcon } from "@everstage/evericons/outlined";
import { ArrowNarrowLeftIcon } from "@everstage/evericons/solid";
import { Row, Col, Space } from "antd";
import { format, parse } from "date-fns";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";

import { RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { formatCurrencyAsNumber } from "~/Utils/CurrencyUtils";
import { EverCheckbox, EverSelect, EverTg, message } from "~/v2/components";
import EverUserEmailModal from "~/v2/components/EverUserEmailModal";

import MapColumns from "./steps/MapColumns";
import UploadCSV from "./steps/UploadCSV";
import Validate from "./steps/Validate";
import { EverButton } from "../ever-button/EverButton";
import { EverHotToastMessage } from "../ever-popups";
import { EverDrawer } from "../EverDrawer";
import { EverModal } from "../EverModal";
import { EverStepper } from "../EverStepper";

import AdjustmentImage from "~/v2/components/ever-upload-v2/images/adjustment.svg?react";

const GET_EXISTING_TEMPLATES = gql`
  query AllApprovalTemplatesByType(
    $excludeInactive: Boolean!
    $entityType: String!
  ) {
    approvalTemplatesByEntityType(
      excludeInactive: $excludeInactive
      entityType: $entityType
    ) {
      templateId
      templateName
      templateDescription
    }
  }
`;
const formatDate = (data, format = "MMM YYYY") => {
  const acceptDateFormats = [
    moment.ISO_8601,
    "MM/D/YYYY",
    "MM-D-YYYY",
    "D/MM/YYYY",
    "D-MM-YYYY",
    "MMM YYYY",
    "YYYY MMM",
    "MMMM YYYY",
    "YYYY MMMM",
  ];

  if (moment(data, acceptDateFormats).isValid()) {
    return moment(data, acceptDateFormats).format(format);
  }

  return data;
};
const isNonEmptyObject = (obj) => obj && Object.keys(obj).length > 0; // Check for non-empty object
const EverUpload = ({
  title,
  instructions = null,
  fieldDefs,
  templateData,
  approvalConfig,
  validateDataService,
  importDataService,
  isVisible,
  handleCloseUpload,
  statementPeriodLabel = null,
  statementPeriod = null,
  ExtraContentStep1,
}) => {
  const { accessToken, email } = useAuthStore();
  const { hasPermissions } = useUserPermissionStore();

  const [cancelModalOpen, setCancelModalOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const [isFormValid, setIsFormValid] = useState(false);

  const handleFormValidityChange = (isValid) => {
    setIsFormValid(isValid);
  };
  const isCommAdjApprovalEnabled =
    approvalConfig?.commissionAdjustmentApprovals?.enabled;

  /*
  Steps:
    0 - Upload CSV
    1 - Map Columns
    2 - Validate
    3 - Import Completed
  */
  const [fileData, setFileData] = useState(null);
  /*
  Example structure of fileData
  {
      name: 'file.csv',
      headers: ['email', 'name', 'age'],
      values: [{email: '<EMAIL>', name: 'Everstage User', age: 23}],
  }
  */
  const [fieldsColumnMap, setFieldsColumnMap] = useState({});
  /*
    fieldsColumnMap: {
      employeeEmailId: 'Employee Email',
      name: 'name',
      age: 'age'
    }
  */
  const [validatedData, setValidatedData] = useState(null);
  /*
  Example structure of validatedData
  {
      records: [{employeeEmailId: '<EMAIL>', name: 'Everstage User', age: 23}],
  }
  */
  const [overrideSelect, setOverrideSelect] = useState(false);
  const [autoApproveCount, setAutoApproveCount] = useState(0);
  const [isDataValidated, setIsDataValidated] = useState(false);
  const [workflowsArray, setWorkflowsArray] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [successImportCount, setSuccessImportCount] = useState(0);
  const [emailIdModalVisible, setEmailIdModalVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState("");
  const [allRecordsCount, setAllRecordsCount] = useState(0);
  const COMM_ADJ_ENTITY_TYPE = "commission_adjustment";
  const { t } = useTranslation();

  useQuery(GET_EXISTING_TEMPLATES, {
    variables: {
      excludeInactive: true,
      entityType: COMM_ADJ_ENTITY_TYPE,
    },
    fetchPolicy: "no-cache",
    onCompleted: (result) => {
      setWorkflowsArray(result?.approvalTemplatesByEntityType);
    },
  });

  const handleBack = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleNext = () => {
    setCurrentStep(1);
  };

  const handleCancelProcess = () => {
    handleCloseUpload();
    setCancelModalOpen(false);
    onResetAll();
  };

  const onResetAll = () => {
    setCurrentStep(0);
    setFileData(null);
    setValidatedData(null);
    setSuccessImportCount(null);
    setAutoApproveCount(0);
    setAllRecordsCount(0);
  };

  const onUploadCSV = (name, headers, values) => {
    let data = {
      name,
      headers,
      values,
    };
    let columnMap = {};
    for (let field of fieldDefs) {
      for (let header of headers) {
        if (header.toLowerCase() === field.label.toLowerCase())
          columnMap[field.key] = header;
      }
    }

    setFileData(data);
    setFieldsColumnMap(columnMap);
  };

  const mapFieldWithHeader = (field, value) =>
    setFieldsColumnMap({ ...fieldsColumnMap, [field]: value });

  const onEditValue = (rawRecordId, field, newValue) => {
    setValidatedData({
      ...validatedData,
      records: validatedData?.records?.map((row) => {
        if (row.raw_record_id === rawRecordId) {
          return { ...row, [field]: newValue };
        }
        return row;
      }),
    });
    setIsDataValidated(false);
  };
  const getRecordsFromFileData = () => {
    const formattedFileData = fileData.values.map((record) => {
      let obj = {};
      for (let field of fieldDefs) {
        if (field.type === "Currency")
          obj[field.key] = formatCurrencyAsNumber(
            record[fieldsColumnMap[field.key]]?.trim?.() ||
              record[fieldsColumnMap[field.key]]
          );
        else if (field.type === "Date")
          obj[field.key] = formatDate(record[fieldsColumnMap[field.key]]);
        else {
          obj[field.key] =
            record[fieldsColumnMap[field.key]] === undefined
              ? ""
              : record[fieldsColumnMap[field.key]].toString().trim();
        }
      }
      return obj;
    });
    return formattedFileData;
  };
  const onValidateData = async () => {
    for (let field of fieldDefs) {
      if (
        (!(field.key in fieldsColumnMap) || !fieldsColumnMap[field.key]) &&
        field.required
      ) {
        toast.custom(
          (t) => (
            <EverHotToastMessage
              type="error"
              description={"Select all required fields."}
              toastId={t.id}
            />
          ),
          { position: "top-center" }
        );
        return;
      }
    }
    setCurrentStep(2);
    const periodDates = statementPeriod.split(" <-> ");
    const psd = format(
      parse(periodDates[0], "yyyy-MM-dd", new Date()),
      "dd-MMM-yyyy"
    );
    const ped = format(
      parse(periodDates[1], "yyyy-MM-dd", new Date()),
      "dd-MMM-yyyy"
    );
    const allRecords = getRecordsFromFileData();
    const response = await validateDataService(
      { psd: psd, ped: ped, records: allRecords },
      accessToken
    );
    setAllRecordsCount(allRecords.length);
    if (response.ok) {
      const resData = await response.json();
      // Parse the records field if it's a stringified JSON
      const parsedRecords = JSON.parse(resData.records || "[]");
      const apporveCount = resData.autoApprovedCount;
      setAutoApproveCount(apporveCount);
      setValidatedData({
        ...resData,
        records: parsedRecords.map((record) => ({
          ...record,
          errors: record.errors || {}, // Ensure errors field exists
        })),
      });
      setIsDataValidated(true);
      toast.custom(
        (t) => (
          <EverHotToastMessage
            type="success"
            description={"Validation successful."}
            toastId={t.id}
          />
        ),
        { position: "top-center" }
      );
    } else {
      toast.custom(
        (t) => (
          <EverHotToastMessage
            type="error"
            description={"Something went wrong. Please Try again."}
            toastId={t.id}
          />
        ),
        { position: "top-center", duration: 1000 }
      );
    }
  };
  const onRevalidate = async () => {
    const periodDates = statementPeriod.split(" <-> ");
    const psd = format(
      parse(periodDates[0], "yyyy-MM-dd", new Date()),
      "dd-MMM-yyyy"
    );
    const ped = format(
      parse(periodDates[1], "yyyy-MM-dd", new Date()),
      "dd-MMM-yyyy"
    );
    const formattedRecords = validatedData?.records
      ?.map((record) => ({
        ...record,
        payee_email: record.payee_email?.trim() || null,
        amount: record.amount?.trim() || null,
        payee_currency: record.payee_currency?.trim() || null,
        reason_description: record.reason_description?.trim() || null,
        reason_category: record.reason_category?.trim() || null,
        commission_plan: record.commission_plan?.trim() || null,
        component: record.component?.trim() || null,
        line_item_id: record.line_item_id?.trim() || null,
      }))
      .filter((record) => {
        const keysToExclude = ["raw_record_id", "errors"];
        return Object.keys(record).some(
          (key) =>
            !keysToExclude.includes(key) &&
            record[key] !== null &&
            record[key] !== undefined &&
            record[key] !== ""
        );
      });
    setAllRecordsCount(formattedRecords.length);
    const response = await validateDataService(
      { psd: psd, ped: ped, records: formattedRecords },
      accessToken
    );
    if (response.ok) {
      const resData = await response.json();
      const parsedRecords = JSON.parse(resData.records || "[]");
      setValidatedData({
        ...resData,
        records: parsedRecords.map((record) => ({
          ...record,
          errors: record.errors || {}, // Ensure errors field exists
        })),
      });
      const apporveCount = resData?.autoApprovedCount;
      setAutoApproveCount(apporveCount);
      setIsDataValidated(true);
      toast.custom(
        (t) => (
          <EverHotToastMessage
            type="success"
            description={"Validation successful."}
            toastId={t.id}
          />
        ),
        { position: "top-center" }
      );
    } else {
      toast.custom(
        (t) => (
          <EverHotToastMessage
            type="error"
            description={"Something went wrong. Please Try again."}
            toastId={t.id}
          />
        ),
        { position: "top-center" }
      );
    }
  };

  const getValidRecordsCount = () => {
    if (!Array.isArray(validatedData?.records)) {
      console.error("validatedData.records is not an array");
      return 0;
    }

    return validatedData.records.filter((row) => {
      // Check if 'errors' is null, undefined, or an empty object
      return !row.errors || Object.keys(row.errors).length === 0;
    }).length;
  };
  const handleSelectionToggle = (e) => {
    setOverrideSelect(e.target.checked);
  };

  const validRecordsCount = getValidRecordsCount();

  const onImportInitiate = () => {
    setModalVisible(true);
  };
  const onImportData = async () => {
    if (isCommAdjApprovalEnabled && selectedWorkflow == "") {
      return message.error("Please select workflow options!");
    }
    setModalVisible(false);
    if (hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS)) {
      setEmailIdModalVisible(true);
    } else {
      submitRequest(email);
    }
  };

  const handleEmailSubmit = (data) => {
    submitRequest(data?.email);
    setEmailIdModalVisible(false);
    handleCloseUpload();
    toast.custom(
      (t) => (
        <EverHotToastMessage
          type="loading"
          description={"Upload in progress..."}
          toastId={t.id}
        />
      ),
      { position: "top-center" }
    );
  };

  function handleWorkflowSelect(id) {
    setSelectedWorkflow(id);
  }

  const submitRequest = async (email) => {
    const periodDates = statementPeriod.split(" <-> ");
    const psd = format(
      parse(periodDates[0], "yyyy-MM-dd", new Date()),
      "dd-MMM-yyyy"
    );
    const ped = format(
      parse(periodDates[1], "yyyy-MM-dd", new Date()),
      "dd-MMM-yyyy"
    );
    const response = await importDataService(
      {
        overrideSelect: overrideSelect,
        selectedWorkflow: selectedWorkflow,
        period_start_date: psd,
        period_end_date: ped,
        records: validatedData?.records.filter(
          (row) => !row.errors || !isNonEmptyObject(row.errors) // No errors or empty errors
        ),
        file_name: fileData.name,
        error_records: validatedData?.records.filter(
          (row) => isNonEmptyObject(row.errors) // Include only records with non-empty errors
        ),
        email_to: email,
      },
      accessToken
    );
    if (response.ok) {
      const resData = await response.json();
      setSuccessImportCount(resData.successCount);
      setModalVisible(false);
      setCurrentStep(0);
      handleCloseUpload();
      onResetAll();
    } else {
      toast.custom(
        (t) => (
          <EverHotToastMessage
            type="error"
            description={"Something went wrong. Please Try again."}
            toastId={t.id}
          />
        ),
        { position: "top-center" }
      );
    }
  };

  useEffect(() => {
    if (!ExtraContentStep1) {
      setIsFormValid(true);
    }
  }, [ExtraContentStep1]);

  return (
    <>
      <EverModal.Confirm
        visible={cancelModalOpen}
        confirmationButtons={[
          <EverButton
            key="cancel"
            color="base"
            onClick={() => setCancelModalOpen(false)}
            type="ghost"
          >
            No
          </EverButton>,
          <EverButton
            key="accept"
            color="error"
            onClick={() => handleCancelProcess()}
          >
            Yes
          </EverButton>,
        ]}
        title="Your import is incomplete!"
        subtitle={t("HANDLE_CANCEL")}
        type="error"
      />

      <EverDrawer
        title={title}
        visible={isVisible}
        onClose={() => {
          currentStep != 0 && currentStep != 2
            ? setCancelModalOpen(true)
            : handleCloseUpload();
          onResetAll();
        }}
        destroyOnClose={true}
        closable={true}
        placement="top"
        height="95vh"
        bodyStyle={{ padding: "0px" }}
        footer={
          <Row>
            <Col
              span={12}
              className="flex text-left text-ever-base-content"
            ></Col>
            <Col span={24} className="text-right">
              {currentStep === 0 && (
                <Space>
                  <EverButton
                    type="filled"
                    color={fileData === null ? "base" : "primary"}
                    onClick={handleNext}
                    disabled={fileData === null}
                    size="medium"
                  >
                    Next
                  </EverButton>
                </Space>
              )}
              {currentStep === 1 && (
                <Space className="flex justify-between">
                  <EverButton
                    type="ghost"
                    color="base"
                    onClick={handleBack}
                    size="medium"
                    prependIcon={<ArrowNarrowLeftIcon />}
                  >
                    Back
                  </EverButton>
                  <EverButton
                    type="filled"
                    onClick={onValidateData}
                    size="medium"
                  >
                    Import and Validate
                  </EverButton>
                </Space>
              )}
              {currentStep === 2 && (
                <>
                  <Space className="flex justify-between">
                    <EverButton
                      type="ghost"
                      color="base"
                      onClick={() => {
                        setValidatedData(null);
                        handleBack();
                      }}
                      size="medium"
                      prependIcon={<ArrowNarrowLeftIcon />}
                    >
                      Back
                    </EverButton>
                    <div className="gap-4 flex">
                      <EverButton
                        type="ghost"
                        color="base"
                        onClick={async () => {
                          await onRevalidate();
                        }}
                        prependIcon={<RefreshIcon />}
                        size="medium"
                      >
                        Revalidate
                      </EverButton>
                      <EverButton
                        type="filled"
                        color={
                          isDataValidated === false || validRecordsCount === 0
                            ? "base"
                            : "primary"
                        }
                        onClick={onImportInitiate}
                        disabled={
                          isDataValidated === false || validRecordsCount === 0
                        }
                      >
                        Import
                      </EverButton>
                    </div>
                  </Space>
                  {isCommAdjApprovalEnabled ? (
                    <EverModal
                      title=""
                      visible={modalVisible}
                      onCancel={() => {
                        setModalVisible(false);
                        setSelectedWorkflow("");
                      }}
                      width={560}
                      destroyOnClose={true}
                      height="360px"
                    >
                      <div className="flex flex-col items-center mt-16">
                        <AdjustmentImage></AdjustmentImage>
                        <div className="flex items-center py-3 px-4 gap-2 border rounded-lg bg-ever-warning-lite mb-4 mt-4">
                          Of the {validRecordsCount} validated records,
                          {autoApproveCount} were automatically approved based
                          on the threshold. Please select an approval workflow
                          for the remaining{" "}
                          {validRecordsCount - autoApproveCount} records
                        </div>
                        <div className="flex mb-4 items-center gap-4">
                          <div className="flex whitespace-nowrap">
                            {" "}
                            Approval Workflow:
                          </div>

                          <EverSelect
                            options={workflowsArray.map((workflow) => ({
                              value: workflow.templateId,
                              label: workflow.templateName,
                            }))}
                            className="w-48"
                            value={selectedWorkflow}
                            onChange={(selectedOption) => {
                              handleWorkflowSelect(selectedOption); // Pass the templateId directly
                            }}
                          />
                        </div>
                        <div className="flex justify-between items-center">
                          <EverCheckbox
                            checked={overrideSelect}
                            onChange={handleSelectionToggle}
                            label={`Override and send all ${validRecordsCount} records for approval`}
                            disabled={autoApproveCount <= 0}
                          ></EverCheckbox>
                        </div>
                        <div className="flex items-center gap-4 mt-4">
                          <EverButton
                            type="ghost"
                            onClick={() => {
                              setModalVisible(false);
                              setSelectedWorkflow("");
                            }}
                          >
                            No, Cancel
                          </EverButton>
                          <EverButton
                            type="filled"
                            className="w-[248px]"
                            onClick={onImportData}
                            disabled={
                              isCommAdjApprovalEnabled && selectedWorkflow == ""
                            }
                          >
                            Yes, Import
                          </EverButton>
                        </div>
                      </div>
                    </EverModal>
                  ) : (
                    <EverModal
                      visible={modalVisible}
                      onCancel={() => setModalVisible(false)}
                      width="560px"
                      height="418px"
                      destroyOnClose={true}
                    >
                      <div className="flex flex-col mt-12 items-center gap-4">
                        <AdjustmentImage></AdjustmentImage>
                        <EverTg.Heading2>
                          You will be able to import only validated records
                        </EverTg.Heading2>
                        <div className="flex items-center py-3 px-4 gap-2 border rounded-lg bg-ever-warning-lite">
                          Proceeding to import {validRecordsCount} records and
                          mail the imported status
                        </div>
                        <div className="flex items-center gap-4  mt-4">
                          <EverButton
                            type="ghost"
                            onClick={() => setModalVisible(false)}
                          >
                            No, Cancel
                          </EverButton>
                          <EverButton
                            type="filled"
                            className="w-[248px]"
                            onClick={onImportData}
                          >
                            Yes, Import
                          </EverButton>
                        </div>
                      </div>
                    </EverModal>
                  )}

                  <EverUserEmailModal
                    isVisible={emailIdModalVisible}
                    onCancel={() => setEmailIdModalVisible(false)}
                    onSubmit={handleEmailSubmit}
                    label="Enter the email address of the user who needs to be notified when the job is complete."
                  />
                </>
              )}
            </Col>
          </Row>
        }
      >
        <div className="flex flex-nowrap items-center bg-ever-base-50 p-4 justify-center">
          <div className="w-6/12">
            <EverStepper
              steps={["Upload CSV", "Map columns", "Import"]}
              size="small"
              current={currentStep}
            />
          </div>
        </div>
        <div className="py-2.5 px-8">
          {currentStep === 0 && (
            <Row justify="center">
              <Col span={16} justify={"center"}>
                {ExtraContentStep1 ? (
                  <ExtraContentStep1
                    onFormValidityChange={handleFormValidityChange}
                  />
                ) : null}
                <UploadCSV
                  fileData={fileData}
                  fieldDefs={fieldDefs}
                  onUploadCSV={onUploadCSV}
                  instructions={instructions}
                  onResetFile={onResetAll}
                  templateData={templateData}
                  disabled={!isFormValid}
                  statementPeriod={statementPeriodLabel}
                />
              </Col>
            </Row>
          )}
          {currentStep === 1 && (
            <MapColumns
              statementPeriod={statementPeriodLabel}
              fieldDefs={fieldDefs}
              fileData={fileData}
              fieldsColumnMap={fieldsColumnMap}
              filedOptions={fileData.headers.map((val) => ({
                label: val,
                value: val,
              }))}
              mapFieldWithHeader={mapFieldWithHeader}
            />
          )}
          {currentStep === 2 && (
            <Validate
              statementPeriod={statementPeriodLabel}
              allRecordsCount={allRecordsCount}
              validatedRecords={validatedData?.records}
              messages={validatedData?.messages}
              fieldDefs={fieldDefs}
              onEditValue={onEditValue}
            />
          )}
        </div>
      </EverDrawer>
    </>
  );
};

export default EverUpload;
