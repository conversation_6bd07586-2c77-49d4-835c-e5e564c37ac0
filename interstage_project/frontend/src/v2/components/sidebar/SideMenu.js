import { DotsVerticalIcon } from "@everstage/evericons/outlined";
import { compact } from "lodash";
import { observer } from "mobx-react";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import { getApprovalConfig } from "~/Api/ApprovalWorkflowService";
import { EVERSIDER_PAGE_NAMES, RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { useModules } from "~/v2/hooks";

import { CustomDropdown } from "./CustomDropdown";
import { getEverMenuElements, getCpqMenuElements } from "./menu-content";
import { MoreMenuDropdown } from "./MoreMenuDropdown";
import { NonRouteButton } from "./NonRouteButton";
import { RouteButton } from "./RouteButton";
import { sidebarMenuIcons, getMenuList } from "./utils";

import "./sidebar.scss";

const GAP_BETWEEN_MENU_AND_USER_ICON = 32;

export const SideMenu = observer(({ userDetailsRef }) => {
  const { hasPermissions } = useUserPermissionStore();
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);

  const {
    showContracts,
    isRevenueRole,
    pendingRequestsCount,
    canUserManageAdmins,
    pendingCommAdjCount,
    pendingPlanApprovalRequestCount,
  } = useEmployeeStore();
  const modules = useModules();

  const { t } = useTranslation();
  const { accessToken } = useAuthStore();
  const [primaryMenuElements, setPrimaryMenuElements] = useState([]);
  const [filteredMenuElements, setFilteredMenuElements] = useState([]);
  const [moreMenuElements, setMoreMenuElements] = useState([]);

  const menuItemRefs = useRef([]);

  const [showApprovalFeature, setShowApprovalFeature] = useState(false);

  const sideBarContainerRef = useRef(null);

  const { data: approvalConfigData } = useReactQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (approvalConfigData?.status === "SUCCESS") {
      const config = approvalConfigData?.data || {};
      setShowApprovalFeature(
        config?.payoutApprovals?.enabled ||
          config?.commissionAdjustmentApprovals?.enabled ||
          false
      );
    }
  }, [approvalConfigData]);

  // Common dependencies for useEffect hooks
  const menuDependencies = [
    isRevenueRole,
    showApprovalFeature,
    approvalConfigData,
    showContracts,
    clientFeatures.loading,
  ];

  // Effect to add and clean up the resize event listener for filtering menu elements
  useEffect(() => {
    window.addEventListener("resize", filterMenuElements);
    return () => {
      window.removeEventListener("resize", filterMenuElements);
    };
  }, menuDependencies);

  // Effect to filter menu elements whenever dependencies change
  useEffect(() => {
    // set timeout makes sure that the dom is rendered with updated data
    setTimeout(() => {
      filterMenuElements();
    }, 100);
  }, menuDependencies);

  const filterMenuElements = () => {
    // Determine the menu items to display based on the current module.
    // If the CPQ module is active, use CPQ-specific menu elements.
    // Otherwise, use the default Ever menu elements.
    const menuItems = modules.isCPQ
      ? getCpqMenuElements()
      : getEverMenuElements(t);

    // following paraments passed to this function
    // 1) menuList
    // 2) RBAC hasPermission fn
    // 3) additional conditions
    const filteredMenuElements = getMenuList(menuItems, hasPermissions, {
      [EVERSIDER_PAGE_NAMES.FORECAST]: () => clientFeatures?.showForecast,
      [EVERSIDER_PAGE_NAMES.SEARCH]: () => clientFeatures?.globalSearch,
      [EVERSIDER_PAGE_NAMES.APPROVALS]: () =>
        clientFeatures.showApprovalFeature && showApprovalFeature,
      [EVERSIDER_PAGE_NAMES.CONTRACTS]: () => showContracts === true,
      [EVERSIDER_PAGE_NAMES.KPI]: () =>
        clientFeatures?.showMetrics && canUserManageAdmins,
      [EVERSIDER_PAGE_NAMES.COMMISSION_ADJUSTMENT_APPROVALS]: () =>
        approvalConfigData?.data?.commissionAdjustmentApprovals?.enabled,
      [EVERSIDER_PAGE_NAMES.PAYOUT_APPROVALS]: () =>
        approvalConfigData?.data?.payoutApprovals?.enabled,
      [EVERSIDER_PAGE_NAMES.CRYSTAL]: () =>
        hasPermissions(RBAC_ROLES.MANAGE_CRYSTAL) || isRevenueRole,
      [EVERSIDER_PAGE_NAMES.DATASHEET]: () => clientFeatures.showDataSourcesV2,
      [EVERSIDER_PAGE_NAMES.DATABOOK]: () => !clientFeatures.showDataSourcesV2,
      [EVERSIDER_PAGE_NAMES.EVERAI]: () => clientFeatures?.enableEverai,
      [EVERSIDER_PAGE_NAMES.TERRITORY_PLANS]: () =>
        clientFeatures?.showTerritoryPlan,
    });

    setPrimaryMenuElements(filteredMenuElements);
    setFilteredMenuElements(filteredMenuElements);
  };

  /**
   * Effect hook to handle menu elements based on maximum container height and visibility.
   *
   * @param {Object} userDetailsRef - React ref for the user details container.
   * @param {Object} sideBarContainerRef - React ref for the sidebar container.
   * @param {Array} primaryMenuElements - Array of menu elements.
   */
  useEffect(() => {
    setMenuElements();
  }, [
    userDetailsRef?.current,
    sideBarContainerRef?.current,
    primaryMenuElements,
  ]);

  const setMenuElements = () => {
    // Maximum height of the menu container based on user details height and the gap between menu and user detail dom.
    const maximumMenuContainerHeight =
      userDetailsRef?.current.getBoundingClientRect().top -
      GAP_BETWEEN_MENU_AND_USER_ICON;

    const newMenuElements = [];
    const moreMenuItems = [];

    //Iterate through each menu element to determine visibility and placement.
    primaryMenuElements.forEach((item, index) => {
      // Check if the bottom of the current menu item is within the visible area of the menu container
      if (
        maximumMenuContainerHeight >=
        menuItemRefs.current[index]?.getBoundingClientRect().bottom
      ) {
        // Item is fully visible in the main menu, so it should be included in the main menu elements
        newMenuElements.push(item);
      } else {
        // Item is not fully visible in the main menu, move it to the "more" menu

        if (moreMenuItems.length === 0) {
          // If "more" menu is empty, move the last item in the main menu to "more"
          // Also, replace the last element of the main menu with the "more" icon (three dots)
          moreMenuItems.push(newMenuElements.pop());
        }
        // Handle SETTINGS icon
        if (item.label === EVERSIDER_PAGE_NAMES.SETTINGS) {
          // SETTINGS icon should always be in the main menu, so move the last item in the main menu to "more" and place the SETTINGS item in the main menu
          moreMenuItems.unshift(newMenuElements.pop());
          newMenuElements.push(item);
        } else {
          moreMenuItems.push(item);
        }
      }
    });

    // Update state only if there are changes in between primaryMenu and newmenu elements
    if (
      JSON.stringify(primaryMenuElements) !== JSON.stringify(newMenuElements)
    ) {
      setPrimaryMenuElements(newMenuElements);
      setMoreMenuElements(moreMenuItems);
    }

    // Clear "more" menu if all menu items are displayed in the main menu
    if (filteredMenuElements.length === newMenuElements.length) {
      setMoreMenuElements([]);
    }
  };

  const badgeList = {
    Approvals: {
      count: pendingRequestsCount + pendingCommAdjCount,
      title: "Total pending requests",
    },
    payoutApprovals: {
      count: pendingRequestsCount,
      title: "Pending Requests",
    },
    commissionAdjApprovals: {
      count: pendingCommAdjCount,
      title: "Pending Commission adjustments Requests",
    },
    Commission: {
      count: pendingPlanApprovalRequestCount,
      title: `Pending ${t("COMMISSION")} Plan Requests`,
    },
  };

  return (
    <div
      className={twMerge(
        "w-full hover:w-[90vw] h-screen top-0 pt-[72px] absolute flex flex-col pb-12 overflow-y-auto scrolling-div pointer-events-none gap-2"
      )}
    >
      <div
        className="menu-container flex flex-col gap-2"
        ref={sideBarContainerRef}
      >
        {primaryMenuElements.map((ele, index) => {
          let returnItem = null;
          if (ele.type == "routingItem") {
            returnItem = (
              <div className="w-fit py-1 pointer-events-auto">
                <RouteButton
                  key={ele.name}
                  data={ele}
                  icon={sidebarMenuIcons[ele.icon].main}
                  activeIcon={sidebarMenuIcons[ele.icon].active}
                  selected={sidebarMenuIcons[ele.icon].selected}
                  badgeList={badgeList}
                />
              </div>
            );
          } else if (ele.type == "nonRoutingItem") {
            returnItem = (
              <div className="w-fit py-1 pointer-events-auto">
                <NonRouteButton
                  key={ele.name}
                  data={ele}
                  icon={sidebarMenuIcons[ele.icon].main}
                  activeIcon={sidebarMenuIcons[ele.icon].active}
                />
              </div>
            );
          } else if (ele.type == "submenu") {
            const submenuList = compact(
              ele.items.map((item) => {
                if (ele.label === EVERSIDER_PAGE_NAMES.APPROVALS) {
                  const config = approvalConfigData?.data;
                  if (item.label === EVERSIDER_PAGE_NAMES.PAYOUT_APPROVALS) {
                    return config?.payoutApprovals?.enabled ? item : null;
                  }
                  if (
                    item.label ===
                    EVERSIDER_PAGE_NAMES.COMMISSION_ADJUSTMENT_APPROVALS
                  ) {
                    return config?.commissionAdjustmentApprovals?.enabled
                      ? item
                      : null;
                  }
                } else {
                  const showSubMenu = hasPermissions(item?.rbacPermission);
                  if (showSubMenu) {
                    return item;
                  }
                  return null;
                }
              })
            );
            if (submenuList.length > 0) {
              returnItem = (
                <div className="flex items-center py-1 pointer-events-auto">
                  <CustomDropdown
                    key={ele.name}
                    icon={sidebarMenuIcons[ele.icon].main}
                    activeIcon={sidebarMenuIcons[ele.icon].active}
                    options={submenuList}
                    title={ele.name}
                    selected={sidebarMenuIcons[ele.icon].selected}
                    badgeList={badgeList}
                    data={ele}
                  />
                  <DotsVerticalIcon className="h-[13px] w-[13px] text-ever-sidebar-icon" />
                </div>
              );
            }
          }
          return (
            <div
              ref={(el) => (menuItemRefs.current[index] = el)}
              key={`menu-item-${ele.label}`}
            >
              {returnItem}
            </div>
          );
        })}
      </div>
      {moreMenuElements.length > 0 && (
        <MoreMenuDropdown menuList={moreMenuElements} />
      )}
    </div>
  );
});

SideMenu.propTypes = {};
