import { CalendarIcon } from "@everstage/evericons/outlined";
import { message } from "antd";
import { observer } from "mobx-react";
import moment from "moment";
import React, { Fragment, useEffect, useState } from "react";
import { useRecoilValue } from "recoil";

import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverButton, EverButtonGroup, EverModal } from "~/v2/components";
import { EverTg } from "~/v2/components/EverTypography";

export const Temp = observer((props) => {
  const { payout, startDate, endDate } = props;
  const fiscalMonthIndex = useRecoilValue(myClientAtom).fiscalStartMonthZero;

  const getPeriodsForFrequency = () => {
    // Find the first period start based on payout frequency
    const getFirstPeriodStart = (date) => {
      let periodStart = date?.clone()?.startOf("month");
      switch (payout) {
        case "0": {
          return date?.clone()?.startOf("week");
        }
        case "1": {
          return periodStart;
        }
        case "2": {
          let monthsSinceFiscal =
            (periodStart.month() - fiscalMonthIndex + 12) % 12;
          while (monthsSinceFiscal % 3 !== 0) {
            periodStart = periodStart.subtract(1, "month");
            monthsSinceFiscal =
              (periodStart.month() - fiscalMonthIndex + 12) % 12;
          }
          return periodStart;
        }
        case "3": {
          let monthsSinceFiscalHalf =
            (periodStart.month() - fiscalMonthIndex + 12) % 12;
          while (monthsSinceFiscalHalf % 6 !== 0) {
            periodStart = periodStart.subtract(1, "month");
            monthsSinceFiscalHalf =
              (periodStart.month() - fiscalMonthIndex + 12) % 12;
          }
          return periodStart;
        }
        case "4": {
          if (periodStart.month() < fiscalMonthIndex) {
            return periodStart
              .subtract(1, "year")
              .month(fiscalMonthIndex)
              .startOf("month");
          }
          return periodStart.month(fiscalMonthIndex).startOf("month");
        }
        default: {
          throw new Error("Invalid payout frequency");
        }
      }
    };

    const periods = [];
    let currentStart = getFirstPeriodStart(startDate?.clone());

    // Define period length based on payout frequency
    const periodLength = {
      0: { unit: "weeks", length: 1 }, // Weekly
      1: { unit: "months", length: 1 }, // Monthly
      2: { unit: "months", length: 3 }, // Quarterly
      3: { unit: "months", length: 6 }, // Semi-annual
      4: { unit: "months", length: 12 }, // Annual
    }[payout];

    // Generate periods
    while (currentStart.isSameOrBefore(endDate)) {
      const currentEnd = currentStart
        ?.clone()
        .add(periodLength.length, periodLength.unit)
        .subtract(1, "day");
      // Only include periods that overlap with the date range
      if (currentEnd.isSameOrAfter(startDate)) {
        periods.push({
          start: currentStart?.clone(),
          end: currentEnd?.clone(),
        });
      }
      currentStart = currentEnd?.clone()?.add(1, "day");
    }

    return periods;
  };

  const periods = getPeriodsForFrequency();

  return (
    <div className="py-4">
      {periods.map((period, index) => (
        <div key={index} className="p-4 bg-gray-50 rounded">
          {period.start.format("D MMM")} - {period.end.format("D MMM YYYY")}
        </div>
      ))}
    </div>
  );
});

const payoutFreq = (accessToken, params) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(params),
  };
  return fetch("/commission_engine/generate_payout_freq", requestOptions)
    .then((res) => {
      return res;
    })
    .catch(() => {
      message.error("There is an error in statements affected");
    });
};

export const StatementAffected = observer((props) => {
  const { startDate, endDate, payeeList, planList } = props;
  const formattedStartDate = startDate.format("D MMM, YYYY");
  const formattedEndDate = endDate.format("D MMM, YYYY");
  const [activeTab, setActiveTab] = useState("0");
  const [showModal, setShowModal] = useState(false);
  const [allTabs, setAllTabs] = useState([false, false, false, false, false]);
  const [dateRanges, setDateRanges] = useState([
    { start_date: null, end_date: null },
    { start_date: null, end_date: null },
    { start_date: null, end_date: null },
    { start_date: null, end_date: null },
    { start_date: null, end_date: null },
  ]);
  const [statements, setStatements] = useState(false);
  const vari =
    "Payout periods align with your configured payout frequency and sync settings";
  const handleCancel = () => {
    setShowModal(false);
  };
  const { accessToken } = useAuthStore();
  useEffect(() => {
    const handlePayoutFreq = async () => {
      const newStartDate = startDate.format("YYYY/MM/DD");
      const newEndDate = endDate.format("YYYY/MM/DD");
      let params = {
        startDate: newStartDate,
        endDate: newEndDate,
        payeeList: payeeList,
        planList: planList,
      };
      const response = await payoutFreq(accessToken, params);
      const responseData = await response.json();
      if (responseData.length === 0) {
        setStatements(false);
      } else {
        setStatements(true);
        const newState = [false, false, false, false, false];
        const newDateRanges = [
          { start_date: null, end_date: null },
          { start_date: null, end_date: null },
          { start_date: null, end_date: null },
          { start_date: null, end_date: null },
          { start_date: null, end_date: null },
        ];
        for (const value of responseData) {
          if (value["payoutFreq"] == "Monthly") {
            newState[1] = true;
            newDateRanges[1] = {
              start_date: moment.utc(value["startDate"]),
              end_date: moment.utc(value["endDate"]),
            };
          } else if (value["payoutFreq"] == "Quarterly") {
            {
              newState[2] = true;
              newDateRanges[2] = {
                start_date: moment.utc(value["startDate"]),
                end_date: moment.utc(value["endDate"]),
              };
            }
          } else if (value["payoutFreq"] == "Halfyearly") {
            {
              newState[3] = true;
              newDateRanges[3] = {
                start_date: moment.utc(value["startDate"]),
                end_date: moment.utc(value["endDate"]),
              };
            }
          } else if (value["payoutFreq"] == "Annual") {
            newState[4] = true;
            newDateRanges[4] = {
              start_date: moment.utc(value["startDate"]),
              end_date: moment.utc(value["endDate"]),
            };
          } else {
            newState[0] = true;
            newDateRanges[0] = {
              start_date: moment.utc(value["startDate"]),
              end_date: moment.utc(value["endDate"]),
            };
          }
        }
        setAllTabs(newState);
        setDateRanges(newDateRanges);
        for (let i = 0; i < 5; i++) {
          if (newState[i]) {
            setActiveTab(String(i));
            break;
          }
        }
      }
    };
    handlePayoutFreq();
  }, [startDate, endDate, payeeList, planList, accessToken]);
  return (
    <Fragment>
      <EverButton
        className="flex justify-between !px-0"
        onClick={setShowModal}
        color="primary"
        type="link"
      >
        Check out statements getting affected by the sync
      </EverButton>
      <EverModal visible={showModal} onCancel={handleCancel}>
        {statements ? (
          <div>
            <EverTg.Heading4 className="text-lg font-medium">
              Statements Affected:
            </EverTg.Heading4>
            <div className="flex pt-4">
              <CalendarIcon className="w-4 h-4 " />
              <EverTg.SubHeading4 className="text-base px-1">
                Selected Period:
              </EverTg.SubHeading4>
              <EverTg.Text className="font-medium">
                {formattedStartDate} - {formattedEndDate}
              </EverTg.Text>
            </div>
            <EverTg.Text className="text-xs text-ever-base-content-mid">
              {vari}
            </EverTg.Text>
            <div className="pt-4">
              <EverButtonGroup
                activeBtnType="text"
                activeBtnColor="base"
                defActiveBtnIndex={parseInt(activeTab)}
                size="medium"
                className="w-mx "
              >
                {allTabs[0] ? (
                  <EverButton
                    className="!pr-4"
                    onClick={() => {
                      setActiveTab("0");
                    }}
                  >
                    Weekly
                  </EverButton>
                ) : (
                  <div></div>
                )}
                {allTabs[1] ? (
                  <EverButton
                    className="!pr-4"
                    onClick={() => {
                      setActiveTab("1");
                    }}
                  >
                    Monthly
                  </EverButton>
                ) : (
                  <div></div>
                )}
                {allTabs[2] ? (
                  <EverButton
                    className="!pr-4"
                    onClick={() => {
                      setActiveTab("2");
                    }}
                  >
                    Quarterly
                  </EverButton>
                ) : (
                  <div></div>
                )}
                {allTabs[3] ? (
                  <EverButton
                    className="!pr-4"
                    onClick={() => {
                      setActiveTab("3");
                    }}
                  >
                    Semi-Annual
                  </EverButton>
                ) : (
                  <div></div>
                )}
                {allTabs[4] ? (
                  <EverButton
                    className="!pr-4"
                    onClick={() => {
                      setActiveTab("4");
                    }}
                  >
                    Annual
                  </EverButton>
                ) : (
                  <div></div>
                )}
              </EverButtonGroup>
            </div>
            <Temp
              payout={activeTab}
              startDate={dateRanges[activeTab]["start_date"]}
              endDate={dateRanges[activeTab]["end_date"]}
            ></Temp>
          </div>
        ) : (
          <EverTg.Heading4>No statements are getting affected</EverTg.Heading4>
        )}
      </EverModal>
    </Fragment>
  );
});
