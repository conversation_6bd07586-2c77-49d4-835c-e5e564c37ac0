import {
  AlertCircleIcon,
  CheckIcon,
  SendIcon,
} from "@everstage/evericons/outlined";
import { cloneDeep, isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, { useState, useEffect, useMemo } from "react";

import { bulkSendEnvelopes } from "~/Api/ContractService";
import { RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { emailValidator } from "~/Utils/Validators";
import {
  EverButton,
  EverDrawer,
  EverLoader,
  EverModal,
  EverTooltip,
  message,
} from "~/v2/components";

import SendEnvelopesSuccessScreen from "./SendEnvelopesSuccessScreen";
import SendEnvelopesView from "./SendEnvelopesView";

const DATATYPE_MAPPING = {
  email: "Email",
  emailaddress: "Email",
  manualemailaddress: "Email",
  number: "Integer",
  date: "Date",
  other: "String",
};

const SendEnvelopeButton = observer(({ templateId, templateName }) => {
  const { accessToken, email: loggedInUser } = useAuthStore();

  const [showDrawer, setShowDrawer] = useState(false);
  const [isSendEnvelopeModified, setIsSendEnvelopeModified] = useState(false);

  const [gridApi, setGridApi] = useState();
  const [rowData, setRowData] = useState([]);
  const [docusignTabs, setDocusignTabs] = useState();
  const [datasheetDetails, setDatasheetDetails] = useState();
  const [isValidating, setIsValidating] = useState(false);
  const [isValidated, setIsValidated] = useState(false);
  const [loadingGridData, setLoadingGridData] = useState(false);
  const [missingMandatoryColumns, setMissingMandatoryColumns] = useState([]);
  const [dataTypesById, setDataTypesById] = useState({});
  const [columnTypeMap, setColumnTypeMap] = useState({});

  const [selectedEnvelopes, setSelectedEnvelopes] = useState([]);
  const [isSendingEnvelopes, setIsSendingEnvelopes] = useState(false);
  const [isEnvelopesSent, setIsEnvelopesSent] = useState(false);

  const { hasPermissions } = useUserPermissionStore();

  const resetState = () => {
    setShowDrawer(false);
    setIsSendEnvelopeModified(false);

    setSelectedEnvelopes([]);
    setIsSendingEnvelopes(false);
    setIsEnvelopesSent(false);

    setGridApi();
    setRowData([]);
    setDocusignTabs();
    setDatasheetDetails();
    setIsValidating(false);
    setIsValidated(false);
    setLoadingGridData(false);
    setMissingMandatoryColumns([]);
    setDataTypesById({});
    setColumnTypeMap({});
  };

  const handleNavigationBlockerClose = (isClosePopup) => {
    if (isClosePopup) {
      resetState();
    }
  };

  const handleDrawerClose = () => {
    if (!isEnvelopesSent && isSendEnvelopeModified) {
      EverModal.confirm({
        title: "Confirm!",
        subtitle: "Are you sure you want to leave this page?",
        okText: "Yes, Close",
        cancelText: "No, Cancel",
        onOk: handleNavigationBlockerClose,
      });
    } else {
      resetState();
    }
  };

  const handleSendEnvelopes = () => {
    const data = [];
    selectedEnvelopes?.forEach((rowObj) => {
      const row = [];
      docusignTabs.forEach((tab) => {
        const field =
          docusignTabsToDatasheetVariablesMapping[tab?.colHeaderName];

        // if the field is undefined; then columnDefs field should have been set to tab.colHeaderName
        const value = field
          ? rowObj[field]?.value
          : rowObj[tab?.colHeaderName]?.value;

        const _tab = cloneDeep(tab);
        _tab["tabValue"] = value || "";
        row.push(_tab);
      });
      data.push(row);
    });

    const requestData = {
      templateId: templateId,
      templateName: templateName,
      emailId: loggedInUser,
      envelopeData: JSON.stringify(data),
    };

    setIsSendingEnvelopes(true);
    bulkSendEnvelopes(requestData, accessToken)
      .then((response) => {
        setIsSendingEnvelopes(false);
        if (response.ok) {
          message.success("Envelope(s) queued");
          setIsEnvelopesSent(true);
        } else throw new Error("Envelope(s) processing failed!");
      })
      .catch((err) => {
        console.error(err);
        message.error({
          content: err + " Please try after sometime",
          duration: 5,
        });
      });
  };

  const handleValidation = () => {
    setIsValidating(true);

    let hasValidationError = false;
    gridApi.forEachNode((node) => {
      const row = node.data;
      const _row = cloneDeep(row);
      let isModified = false;
      let hasRowError = false;
      const emailCols = {};
      for (let field in row) {
        const docusignTab = datasheetVariablesToDocusignTabsMapping[field];
        if (docusignTab) {
          const tabType = docusignTab.tabType;
          const isMandatory =
            docusignTab.optional === "false" || docusignTab.required === "true";
          const value = row[field].value;
          console.log(value, isMandatory && (!value || value === "-"));

          // checking for data(present or not) in mandatory fields
          if (isMandatory && (!value || value === "-")) {
            hasValidationError = true;
            hasRowError = true;
            isModified = true;
            _row[field] = {
              ...row[field],
              validationStatus: "error",
              validationMessage: "Mandatory Field - No Data",
            };
          }

          // checking for email subject length; must not exceed 100 characters
          else if (tabType === "manualemailsubject") {
            if (String(value).length > 100) {
              console.log("email subject length exceeded", value);
              hasValidationError = true;
              hasRowError = true;
              isModified = true;
              _row[field] = {
                ...row[field],
                validationStatus: "error",
                validationMessage:
                  "Email subject must not exceed 100 characters",
              };
            }
          }

          // check for valid emails
          else if (tabType === "emailaddress") {
            if (!emailValidator(value)) {
              console.log("invalid email", value);
              hasValidationError = true;
              hasRowError = true;
              isModified = true;
              _row[field] = {
                ...row[field],
                validationStatus: "error",
                validationMessage: "Invalid email format",
              };
            }
          }

          // check for valid emails (emails which are used to send)
          else if (tabType === "manualemailaddress") {
            if (!emailValidator(value)) {
              console.log("invalid email", value);
              hasValidationError = true;
              hasRowError = true;
              isModified = true;
              _row[field] = {
                ...row[field],
                validationStatus: "error",
                validationMessage: "Invalid email format",
              };
            } else {
              emailCols[field] = row[field];
            }
          }
        }
      }

      // check for all unique email in an envelope
      const emails = Object.values(emailCols).map((obj) => obj.value);
      if (emails.length >= 0 && emails.length !== new Set(emails).size) {
        console.log("duplicate emails", emails);
        hasValidationError = true;
        hasRowError = true;
        isModified = true;
        // row error feature
        for (let field in emailCols) {
          _row[field] = {
            ...row[field],
            validationStatus: "error",
            validationMessage:
              "All Recipients' email must be unique in an envelope!",
          };
        }
      } else {
        for (let field in emailCols) {
          if (_row[field]["validationStatus"] !== "neutral") {
            isModified = true;
            _row[field] = {
              ...row[field],
              validationStatus: "neutral",
              validationMessage: null,
            };
          }
        }
      }

      // show error(if present) at Primary key(Envelope ID) cell
      const primaryKeyTabLabel = docusignTabs.find(
        (tab) => tab.tabType === "manualprimarykey"
      ).tabLabel;
      const primaryKeyField =
        docusignTabsToDatasheetVariablesMapping[primaryKeyTabLabel];

      if (!isEmpty(primaryKeyField)) {
        if (hasRowError) {
          isModified = true;
          _row[primaryKeyField] = {
            ...row[primaryKeyField],
            validationStatus: "error",
            validationMessage: "Error in this envelope",
          };
        } else if (_row[primaryKeyField]["validationStatus"] !== "neutral") {
          isModified = true;
          _row[primaryKeyField] = {
            ...row[primaryKeyField],
            validationStatus: "neutral",
            validationMessage: null,
          };
        }
      }

      if (isModified) node.setData(_row); // update row data in grid
    });

    // check for missing mandatory columnn
    const missingColumns = Object.keys(
      docusignTabsToDatasheetVariablesMapping
    ).filter((tab) => {
      return docusignTabsToDatasheetVariablesMapping[tab] === undefined;
    });

    const missingMandatoryColumns = missingColumns.filter((col) => {
      const _docusignTab = docusignTabs.find(
        (tab) => tab.colHeaderName.trim() === col.trim()
      );
      return (
        _docusignTab.required === "true" || _docusignTab.optional === "false"
      );
    });

    if (missingMandatoryColumns.length > 0) {
      hasValidationError = true;
    }
    setMissingMandatoryColumns(missingMandatoryColumns);
    if (hasValidationError) {
      message.error({
        content: "Errors detected. Fix them to proceed further",
        duration: 2,
      });
    } else {
      setIsValidated(true);
      message.success({
        content: "Validation Successful!",
        duration: 2,
      });
    }

    setIsValidating(false);
  };

  const datasheetVariablesToDocusignTabsMapping = useMemo(() => {
    const map = {};
    if (docusignTabs) {
      datasheetDetails?.variables.forEach((variable) => {
        map[variable.systemName] = docusignTabs.find(
          (tab) => tab.colHeaderName.trim() === variable.displayName.trim()
        );
      });
    }
    return map;
  }, [datasheetDetails, docusignTabs]);

  const docusignTabsToDatasheetVariablesMapping = useMemo(() => {
    const map = {};
    docusignTabs?.forEach((tab) => {
      map[tab.colHeaderName] = datasheetDetails?.variables.find(
        (variable) => variable.displayName.trim() === tab.colHeaderName.trim()
      )?.systemName;
    });

    return map;
  }, [datasheetDetails, docusignTabs]);

  const columnDefs = useMemo(() => {
    let _columnDefs = [];
    (docusignTabs || []).forEach((tab) => {
      const fieldName =
        docusignTabsToDatasheetVariablesMapping[tab.colHeaderName] ||
        tab.colHeaderName;

      // for primary key column
      if (tab.tabType === "manualprimarykey") {
        _columnDefs.push({
          colId: tab.colHeaderName,
          headerName: tab.colHeaderName,
          field: fieldName,
          lockPosition: "left",
          pinned: "left",
          lockPinned: true,
          suppressColumnsToolPanel: true,
          headerComponentParams: {
            menuIcon:
              dataTypesById[
                columnTypeMap[
                  docusignTabsToDatasheetVariablesMapping[tab.colHeaderName]
                ]
              ],
            ...(missingMandatoryColumns.includes(tab.colHeaderName) && {
              customSuffixIcon: (
                <EverTooltip title="Mandatory Field - Column missing in datasheet">
                  <div className="flex items-center mx-1">
                    <AlertCircleIcon className="w-5 h-5 text-ever-error" />
                  </div>
                </EverTooltip>
              ),
            }),
          },
          editable: false,
          sortable: true,
          headerCheckboxSelection: isValidated,
          checkboxSelection: isValidated,
          valueGetter: (params) => params.data[fieldName]?.value || "",
        });
        return;
      }

      // for remaining all columns
      _columnDefs.push({
        colId: tab.colHeaderName,
        headerName: tab.colHeaderName,
        field: fieldName,
        headerComponentParams: {
          menuIcon: DATATYPE_MAPPING[tab.tabType] || DATATYPE_MAPPING.other,
          ...(missingMandatoryColumns.includes(tab.colHeaderName) && {
            customSuffixIcon: (
              <EverTooltip title="Mandatory Field - Column missing in datasheet">
                <div className="flex items-center mx-1">
                  <AlertCircleIcon className="w-5 h-5 text-ever-error" />
                </div>
              </EverTooltip>
            ),
          }),
        },
        editable: true,
        valueGetter: (params) => params.data[fieldName]?.value || "",
        valueSetter: (params) => {
          setIsValidated(false);
          params.data[fieldName] = {
            value: params.newValue,
            validationStatus: "neutral",
            validationMessage: null,
          };
          return true;
        },
        cellRenderer: (params) => {
          const field = params.colDef?.field;
          const isError = params.data[field]?.validationStatus === "error";
          const msg = params.data[field]?.validationMessage;

          return isError ? (
            <EverTooltip title={msg}>
              <div
                style={{ width: "100%", height: "100%" }} // eslint-disable-line
              >
                {params.value}
              </div>
            </EverTooltip>
          ) : (
            params.value
          );
        },
      });
    });
    return _columnDefs;
  }, [
    docusignTabs,
    docusignTabsToDatasheetVariablesMapping,
    missingMandatoryColumns,
    columnTypeMap,
    dataTypesById,
    isValidated,
  ]);

  useEffect(() => {
    if (selectedEnvelopes?.length > 0 && gridApi) {
      gridApi.deselectAll();
    }
  }, [isValidated, gridApi]);

  useEffect(() => {
    if (gridApi) {
      gridApi.refreshCells();
      gridApi.autoSizeColumns([columnDefs[0]?.colId]);
    }
  }, [columnDefs, gridApi]);

  return (
    <>
      <div>
        <EverTooltip
          title={
            !hasPermissions(RBAC_ROLES.VIEW_DATABOOK)
              ? "You need permission to view databooks to be able to send envelopes. Please reach out to your administrator"
              : ""
          }
        >
          <EverButton
            size="small"
            disabled={!hasPermissions(RBAC_ROLES.VIEW_DATABOOK)}
            onClick={() => {
              setIsEnvelopesSent(false);
              setShowDrawer(true);
            }}
          >
            Send envelopes
          </EverButton>
        </EverTooltip>
      </div>
      {hasPermissions(RBAC_ROLES.VIEW_DATABOOK) && (
        <EverDrawer
          title="Send envelopes"
          placement="top"
          onClose={handleDrawerClose}
          visible={showDrawer}
          destroyOnClose={true}
          footer={
            !isEnvelopesSent && (
              <div className="flex items-center justify-between">
                <span>
                  {isValidated && `${selectedEnvelopes?.length} selected`}
                </span>
                <div className="flex items-center gap-2">
                  <EverButton
                    size="small"
                    color="base"
                    onClick={handleDrawerClose}
                  >
                    Cancel
                  </EverButton>
                  <EverButton
                    size="small"
                    onClick={handleValidation}
                    color={
                      isValidating || rowData.length === 0 || loadingGridData
                        ? "base"
                        : "primary"
                    }
                    disabled={
                      loadingGridData ||
                      isValidating ||
                      isValidated ||
                      rowData.length === 0
                    }
                    icon={isValidated && <CheckIcon />}
                    loading={isValidating}
                  >
                    {isValidating
                      ? "Validating.."
                      : isValidated
                      ? "Validated"
                      : "Validate"}
                  </EverButton>
                  <EverButton
                    size="small"
                    onClick={handleSendEnvelopes}
                    disabled={
                      selectedEnvelopes?.length === 0 ||
                      isSendingEnvelopes ||
                      !isValidated
                    }
                    color={
                      selectedEnvelopes?.length === 0 ||
                      isSendingEnvelopes ||
                      !isValidated
                        ? "base"
                        : "primary"
                    }
                    icon={<SendIcon />}
                  >
                    Send envelopes
                  </EverButton>
                </div>
              </div>
            )
          }
        >
          {isSendingEnvelopes ? (
            <Loading />
          ) : (
            <>
              {isEnvelopesSent ? (
                <SendEnvelopesSuccessScreen
                  count={selectedEnvelopes?.length || 0}
                />
              ) : (
                <SendEnvelopesView
                  templateId={templateId}
                  loggedInUser={loggedInUser}
                  setIsSendEnvelopeModified={setIsSendEnvelopeModified}
                  rowData={rowData}
                  setRowData={setRowData}
                  setDocusignTabs={setDocusignTabs}
                  setDatasheetDetails={setDatasheetDetails}
                  columnDefs={columnDefs}
                  gridApi={gridApi}
                  setGridApi={setGridApi}
                  isValidating={isValidating}
                  setMissingMandatoryColumns={setMissingMandatoryColumns}
                  dataTypesById={dataTypesById}
                  setDataTypesById={setDataTypesById}
                  columnTypeMap={columnTypeMap}
                  setColumnTypeMap={setColumnTypeMap}
                  setIsValidated={setIsValidated}
                  setIsValidating={setIsValidating}
                  loadingGridData={loadingGridData}
                  setLoadingGridData={setLoadingGridData}
                  setSelectedEnvelopes={setSelectedEnvelopes}
                />
              )}
            </>
          )}
        </EverDrawer>
      )}
    </>
  );
});

const Loading = () => {
  return <EverLoader indicatorType="spinner" />;
};

export default SendEnvelopeButton;
