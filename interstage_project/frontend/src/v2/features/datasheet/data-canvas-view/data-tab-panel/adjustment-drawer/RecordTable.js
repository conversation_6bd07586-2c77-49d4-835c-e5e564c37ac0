import { MinusCircleIcon } from "@everstage/evericons/solid";
import { AgGridReact } from "ag-grid-react";
import { format, isValid, parse } from "date-fns";
import { cloneDeep, isEmpty } from "lodash";
import React, { useEffect, useRef, useState } from "react";

import { DATA_TYPES_TO_BE_IGNORED_IN_MODULE } from "~/Enums";
import { EverTg, EverTooltip, IconButton, message } from "~/v2/components";
import { CustomHeader } from "~/v2/components/ag-grid";
import {
  DateCellEditor,
  DropDownCellEditor,
  NumericCellEditor,
} from "~/v2/components/ag-grid/cellEditors";
import { adjustColumnWidth } from "~/v2/components/ag-grid/ever-ag-grid-callbacks";
import { getDefaultOptions } from "~/v2/components/ag-grid/ever-ag-grid-options";
import { formatDate } from "~/v2/components/ever-formatter/EverFormatter";

import { ADJUSTMENT_TYPE_ENUMS } from "../../../utils";

function StrikeThroughComponent({ ele, oldValue, newValue }) {
  if (ele.dataType === "Boolean") {
    return (
      <EverTooltip title={"edit"}>
        <div className="flex flex-col">
          <EverTg.Caption.Medium className="line-through text-ever-warning-hover">
            {oldValue?.toString()}
          </EverTg.Caption.Medium>
          <EverTg.SubHeading4 className="text-ever-base-content">
            {newValue?.toString()}
          </EverTg.SubHeading4>
        </div>
      </EverTooltip>
    );
  }
  const dateNew = new Date(newValue);
  const dateOld = new Date(oldValue);

  return (
    <EverTooltip title={"edit"}>
      <div className="flex flex-col">
        <EverTg.Caption.Medium className="line-through text-ever-warning-hover">
          {ele.dataType === "Date" && oldValue
            ? isValid(dateOld)
              ? formatDate({ date: oldValue })
              : ""
            : oldValue}
        </EverTg.Caption.Medium>
        <EverTg.SubHeading4 className="text-ever-base-content">
          {ele.dataType === "Date" && newValue
            ? isValid(dateNew)
              ? formatDate({ date: newValue })
              : ""
            : newValue}
        </EverTg.SubHeading4>
      </div>
    </EverTooltip>
  );
}

/**
 * RecordTable component for displaying and managing a table of records.
 * This table can have editable cells or view only cells based on the requirement.
 *
 * @param {Object} props - The properties for the component.
 * @param {Array} props.rowData - The data for the rows.
 * @param {Array} props.columns - The column definitions.
 * @param {string} props.type - The type of table (e.g., "EDIT", "SPLIT_EDIT", "VIEW").
 * @param {Function} props.removeSplitTableRow - Function to remove a split table row.
 * @param {Object} props.adjustment - The current adjustment state.
 * @param {Function} props.setAdjustment - Function to set the adjustment state.
 * @param {Array} props.primaryVariables - Primary variables for the datasheet.
 * @returns {JSX.Element} - The rendered component.
 */

export function RecordTable({
  rowData,
  columns,
  type,
  removeSplitTableRow,
  adjustment,
  setAdjustment,
  primaryVariables,
}) {
  const [editableColumns, setEditableColumns] = useState(columns);
  const gridApi = useRef(null);
  const updatedValuesRef = useRef(adjustment?.data || []);
  const updatedAdjustmentTypeRef = useRef(adjustment?.operation);
  const updatedAdjustmentScopeRef = useRef(adjustment?.scope);

  function addAdjustmentDataValues(newValues, index, dataType) {
    Object.keys(newValues).map((key) => {
      if (dataType == "Date") {
        if (newValues[key]) {
          const parsedDate = parse(newValues[key], "dd MMM yyyy", new Date());
          newValues[key] = format(parsedDate, "yyyy-MM-dd'T'HH:mm:ss");
        } else {
          newValues[key] = null;
        }
      }
    });

    if (dataType === "Boolean") {
      Object.keys(newValues).map((key) => {
        newValues[key] = JSON.parse(newValues[key]);
      });
    }

    if (type === ADJUSTMENT_TYPE_ENUMS.SPLIT_EDIT) {
      let arr = updatedValuesRef.current;
      arr[index] = arr[index] ? { ...arr[index], ...newValues } : newValues;
      arr = arr.map((item, idx) =>
        item === undefined && idx <= index ? {} : item
      );
      while (arr.length !== rowData.length) {
        arr.push({});
      }
      updatedValuesRef.current = [...arr];
      setAdjustment({
        ...adjustment,
        data: [...arr],
        operation: updatedAdjustmentTypeRef.current,
        scope: updatedAdjustmentScopeRef.current,
      });
    } else {
      updatedValuesRef.current = {
        ...updatedValuesRef.current,
        ...newValues,
      };
      setAdjustment({
        ...adjustment,
        data: updatedValuesRef.current,
        operation: updatedAdjustmentTypeRef.current,
        scope: updatedAdjustmentScopeRef.current,
      });
    }
  }

  function removeAdjustmentDataValues(key, index) {
    if (type === ADJUSTMENT_TYPE_ENUMS.SPLIT_EDIT) {
      const adjustmentData = cloneDeep(updatedValuesRef.current);
      if (adjustmentData && adjustmentData[index]) {
        delete adjustmentData[index][key];
      }
      setAdjustment((prev) => {
        return {
          ...prev,
          data: adjustmentData,
          operation: updatedAdjustmentTypeRef.current,
          scope: updatedAdjustmentScopeRef.current,
        };
      });
    } else {
      const dataObj = cloneDeep(updatedValuesRef.current);
      if (dataObj && dataObj[key]) {
        delete dataObj[key];
      }
      setAdjustment((prev) => {
        return {
          ...prev,
          data: dataObj,
          operation: updatedAdjustmentTypeRef.current,
          scope: updatedAdjustmentScopeRef.current,
        };
      });
    }
  }

  // Update the adjustment data when the adjustment changes
  useEffect(() => {
    updatedValuesRef.current = adjustment?.data || [];
    updatedAdjustmentTypeRef.current = adjustment?.operation || "";
    updatedAdjustmentScopeRef.current = adjustment?.scope || "";
  }, [adjustment]);

  useEffect(() => {
    const cols = cloneDeep(columns);
    const newCols = [];

    // When the type of adjustment is Split or Update adding the columns which have editable cells
    // This also has the conditions for columns which are a primary key or Calculated field. These should be non editable
    if (
      type === ADJUSTMENT_TYPE_ENUMS.EDIT ||
      type === ADJUSTMENT_TYPE_ENUMS.SPLIT_EDIT
    ) {
      for (let ele of cols) {
        let cellEditor = "agTextCellEditor";
        let cellEditorParams = {};

        // Assigning cell editors based on cell datatype.
        if (ele.dataType === "Date") {
          cellEditor = "dateCellEditor";
          cellEditorParams = {
            format: ele.format,
            isCellEditor: true,
            allowClear: true,
            bordered: true,
            style: {
              width: "140px",
              height: "36px",
            },
          };
        } else if (
          ele.dataType === "Integer" ||
          ele.dataType === "Percentage"
        ) {
          cellEditor = "numericCellEditor";
          cellEditorParams = {
            isCellEditor: true,
            allowNegative: false,
            useDecimals: true,
          };
        } else if (ele.dataType === "Boolean") {
          cellEditor = "dropDownCellEditor";
          cellEditorParams = {
            isCellEditor: true,
            allowClear: false,
            optionData: [
              { label: "true", value: "true" },
              { label: "false", value: "false" },
            ],
          };
        }
        // Setting if cell is editable based on the condition
        ele.editable = true;

        // Suppress the column menu to hide the pin column dropdown
        ele.suppressMenu = true;

        if (primaryVariables.includes(ele.field)) {
          ele.editable = false;
          ele.cellClass = "selected-gray bg-ever-base-50";
          ele.suppressCellSelection = true;
          ele.valueGetter = (params) => {
            const { colDef, data } = params;
            const dataType = colDef.dataType;
            if (dataType === "Date" && data[colDef.field]) {
              return formatDate({
                date: data[colDef?.field],
              });
            } else if (dataType == "Boolean") {
              return data[colDef.field]?.toString();
            }
            return data[colDef.field];
          };
          ele.cellRenderer = (params) => {
            return (
              <EverTooltip title="Cannot edit primary keys">
                <div className="w-full bg-ever-base-50">
                  {params.value !== null &&
                  params.value !== undefined &&
                  params.value !== ""
                    ? params.value
                    : "-"}
                </div>
              </EverTooltip>
            );
          };
          newCols.push({ ...ele });
          continue;
        }
        if (ele.headerComponentParams?.isCalculatedField) {
          ele.editable = false;
          ele.cellClass = "selected-gray bg-ever-base-50";
          ele.suppressCellSelection = true;
          ele.valueGetter = (params) => {
            const { colDef, data } = params;
            const dataType = colDef.dataType;
            if (dataType === "Date" && data[colDef.field]) {
              return formatDate({
                date: data[colDef?.field],
              });
            } else if (dataType == "Boolean") {
              return data[colDef.field]?.toString();
            }
            return data[colDef.field];
          };
          ele.cellRenderer = (params) => {
            return (
              <EverTooltip title="Cannot edit calculated fields">
                <div className="w-full bg-ever-base-50">
                  {params.value !== null &&
                  params.value !== undefined &&
                  params.value !== ""
                    ? params.value
                    : "-"}
                </div>
              </EverTooltip>
            );
          };
          newCols.push({ ...ele });
          continue;
        }
        if (
          ele.dataType &&
          DATA_TYPES_TO_BE_IGNORED_IN_MODULE.DATASHEET_ADJUSTMENT.includes(
            ele.dataType
          )
        ) {
          ele.editable = false;
          ele.cellClass = "selected-gray bg-ever-base-50";
          ele.suppressCellSelection = true;
          ele.valueGetter = (params) => {
            const { colDef, data } = params;
            const dataType = colDef.dataType;
            if (dataType === "Date" && data[colDef.field]) {
              return formatDate({
                date: data[colDef.field],
              });
            } else if (dataType == "Boolean") {
              return data[colDef.field]?.toString();
            }
            return data[colDef.field];
          };
          ele.cellRenderer = (params) => {
            return (
              <EverTooltip
                title={`Cannot edit ${ele.dataType?.toLowerCase()} fields`}
              >
                <div className="w-full bg-ever-base-50">
                  {params.value !== null &&
                  params.value !== undefined &&
                  params.value !== ""
                    ? params.value
                    : "-"}
                </div>
              </EverTooltip>
            );
          };
          newCols.push({ ...ele });
          continue;
        }
        ele.cellClass = "cursor-text";
        ele.cellEditor = cellEditor;
        if (!isEmpty(cellEditorParams)) ele.cellEditorParams = cellEditorParams;

        // Setting the valuesetter which gets the old and new values and makes the comparision
        ele.valueSetter = (params) => {
          let { data, newValue, colDef, oldValue, node, api } = params;
          const isValidF = isValidField(ele.dataType, newValue);

          if (!isValidF) {
            message.error(`Please enter a valid ${colDef.dataType}`);
            setTimeout(() => {
              api.startEditingCell({
                rowIndex: node.rowIndex,
                colKey: colDef.field,
              });
            }, 0);
            return;
          }
          data.areValuesUpdated = true;
          if (data.oldValues) {
            if (!data.oldValues[colDef.field]) {
              data.oldValues[colDef.field] = oldValue;
            }
          } else {
            data.oldValues = { [colDef.field]: oldValue };
          }
          data[colDef.field] =
            colDef.dataType === "Integer" || ele.dataType === "Percentage"
              ? Number(newValue)
              : newValue;

          if (newValue === data.oldValues[colDef.field]) {
            delete data.oldValues[colDef.field];
            removeAdjustmentDataValues(colDef.field, node.rowIndex);
            return false;
          }

          if (ele.dataType === "Boolean") {
            let bool = newValue === "true";
            if (bool === data.oldValues[colDef.field]) {
              delete data.oldValues[colDef.field];
              removeAdjustmentDataValues(colDef.field, node.rowIndex);
              return false;
            }
          }

          if (
            (colDef.dataType === "Integer" || ele.dataType === "Percentage") &&
            data.oldValues[colDef.field] === Number(newValue)
          ) {
            delete data.oldValues[colDef.field];
            removeAdjustmentDataValues(colDef.field, node.rowIndex);
            return false;
          }
          if (colDef.dataType === "Date" && data.oldValues[colDef.field]) {
            if (isValid(new Date(data.oldValues[colDef.field]))) {
              const formattedOldDate = format(
                new Date(data.oldValues[colDef.field]),
                "dd MMM yyyy",
                {
                  timeZone: "UTC",
                }
              );
              if (formattedOldDate === newValue) {
                delete data.oldValues[colDef.field];
                removeAdjustmentDataValues(colDef.field, node.rowIndex);
                return false;
              }
            }
          }
          addAdjustmentDataValues(
            { [colDef.field]: newValue },
            node.rowIndex,
            colDef.dataType
          );
          return true;
        };

        // Setting the cellRenderer which gets both old and new values to display in ui
        ele.cellRenderer = (params) => {
          const {
            data: { areValuesUpdated, oldValues },
            colDef,
            value,
            data,
          } = params;
          const originalValues = data.original_values;

          if (
            areValuesUpdated &&
            oldValues &&
            oldValues[colDef.field] !== undefined
          ) {
            return (
              <StrikeThroughComponent
                oldValue={oldValues[colDef.field]}
                newValue={value}
                ele={{ ...ele }}
              />
            );
          }

          if (
            originalValues &&
            Object.keys(originalValues).length > 0 &&
            originalValues[colDef.field] !== undefined
          ) {
            return (
              <StrikeThroughComponent
                oldValue={originalValues[colDef.field]}
                newValue={value}
                ele={{ ...ele }}
              />
            );
          }

          if (ele.dataType === "Boolean") {
            return (
              <EverTooltip title="Click to edit">
                <div className="w-full h-full">{value?.toString()}</div>
              </EverTooltip>
            );
          }

          return (
            <EverTooltip title="Click to edit">
              <div className="w-full h-full">
                {ele.dataType === "Date" && value
                  ? format(new Date(value), "dd MMM yyyy", {
                      timeZone: "UTC",
                    })
                  : value
                  ? value
                  : "-"}
              </div>
            </EverTooltip>
          );
        };

        // Pushing the updated element of columnDefs
        newCols.push({ ...ele });
      }
    }

    // When the type of adjustment is View
    if (type === ADJUSTMENT_TYPE_ENUMS.VIEW) {
      for (let ele of cols) {
        ele.cellClass = "bg-ever-base-50";
        ele.suppressMenu = true;
        newCols.push({ ...ele });
      }
    }

    // When the type of adjustment is Split and there are more than 2 rows
    // Pushinh extra column to show the remove row button
    type === ADJUSTMENT_TYPE_ENUMS.SPLIT_EDIT &&
      rowData.length > 2 &&
      newCols.push({
        headerName: "",
        field: "action",
        pinned: "right",
        lockPinned: true,
        sortable: false,
        serverSideSortable: false,
        filter: false,
        width: 56,
        maxWidth: 56,
        cellStyle: { paddingRight: 0, paddingLeft: "16px" },
        headerClass: "!px-0",
        suppressColumnsToolPanel: true,
        suppressMenu: true, // Suppress the column menu to hide the pin column dropdown
        valueGetter: (params) => {
          return params.data;
        },
        cellRenderer: (params) => {
          if (params.node.rowIndex > 1) {
            return (
              <IconButton
                type="text"
                color="error"
                icon={<MinusCircleIcon className="!w-4 !h-4" />}
                size="small"
                className="!w-6 !h-6"
                onClick={() => removeSplitTableRow(params.node.rowIndex)}
              />
            );
          }
          return "";
        },
      });

    setEditableColumns(newCols);

    // Adjusting the column width on every re-render
    adjustColumnWidth(gridApi.current);
  }, [columns, rowData.length, type]);

  const onCellValueChanged = (params) => {
    const { colDef, newValue, oldValue, data, node, api } = params;
    const isValid = isValidField(colDef.dataType, newValue);

    if (colDef.dataType == "Boolean" && newValue == null) {
      return;
    }

    if (!isValid) {
      message.error(`Please enter a valid ${colDef.dataType}`);
      data[colDef.field] = oldValue;
      data.areValuesUpdated = false;
      if (data.oldValues) {
        delete data.oldValues[colDef.field];
      }
      api.refreshCells({ rowNodes: [node], force: true });
    } else {
      addAdjustmentDataValues(
        { [colDef.field]: newValue },
        node.rowIndex,
        colDef.dataType
      );
    }
  };

  useEffect(() => {
    if (gridApi.current.api) {
      gridApi?.current?.api?.forEachNode(function (node) {
        if (node.data.areValuesUpdated) {
          node.data.areValuesUpdated = false;
          Object.keys(node.data.oldValues).forEach((key) => {
            node.setDataValue(key, node.data.oldValues[key] || node.data[key]);
          });
        }
      });
    }
  }, [adjustment.operation]);

  return (
    <div className="ag-theme-material no-border w-full datasheet-table datasheet-v2-table min-h-20 no-min-height">
      <AgGridReact
        {...getDefaultOptions({
          type: "sm",
        })}
        ref={gridApi}
        columnDefs={editableColumns}
        rowData={rowData}
        domLayout="autoHeight"
        stopEditingWhenCellsLoseFocus={true}
        suppressRowClickSelection={true}
        undoRedoCellEditing={true}
        singleClickEdit={true}
        onCellValueChanged={onCellValueChanged}
        components={{
          agColumnHeader: (params) => CustomHeader(params, "small"),
          numericCellEditor: NumericCellEditor,
          dateCellEditor: DateCellEditor,
          dropDownCellEditor: DropDownCellEditor,
        }}
        getContextMenuItems={() => {
          return ["autoSizeAll"];
        }}
      />
    </div>
  );
}

function isValidField(dataType, value) {
  if (isEmpty(value)) return true;
  const mailformat = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
  switch (dataType) {
    case "String":
      return value.trim() ? true : false;
    case "Email":
      return value && value.match(mailformat) ? true : false;
    case "Integer":
      return value && value.match(/^-?\d*(\.\d+)?$/) ? true : false;
    case "Percentage":
      return value && value.match(/^-?\d*(\.\d+)?$/) ? true : false;
    case "Date":
      return value ? true : false;
    case "Boolean":
      return true;
    default:
      return value && value.match(/^-?\d*(\.\d+)?$/) ? true : false;
  }
}
