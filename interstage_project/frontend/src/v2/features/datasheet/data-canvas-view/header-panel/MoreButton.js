import {
  DataflowIcon,
  InfoCircleIcon,
  Trash03Icon,
  UserEditIcon,
  UploadIcon,
  CopyIcon,
  ChevronRightIcon,
} from "@everstage/evericons/outlined";
import { DotsVerticalIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu } from "antd";
import { observer } from "mobx-react";
import React, { useContext, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation } from "react-query";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  RBAC_ROLES,
  ANALYTICS_EVENTS,
  ANALYTICS_PROPERTIES,
  DATASHEET_VIEW_BY,
} from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverButton,
  EverHotToastMessage,
  EverTg,
  toast,
  EverDivider,
  EverModal,
  EverRadio,
  EverPopover,
  Space,
} from "~/v2/components";

import { ManagePermissionDrawer } from "./manage-permission-drawer/ManagePermissionDrawer";
import { DatasheetContext } from "../../DatasheetStore";
import {
  cloneDatasheet,
  exportDatasheet,
  datasheetExportV2,
} from "../../restApi";

/**
 * Component for displaying a dropdown menu containg actions for datasheet.
 *
 * @param {Object} props - The props object containing the following properties:
 * @returns {JSX.Element} React component
 */

export const MoreButton = observer(
  ({
    handleUpdateDatasheet,
    accessToken,
    showGenerateSheetModal,
    setShowGenerateSheetModal,
    activeKey,
    viewBy,
    pivotConfig,
  }) => {
    const { t } = useTranslation();
    const datasheetStore = useContext(DatasheetContext);
    const { hasPermissions } = useUserPermissionStore();
    const myAtom = useRecoilValue(myClientAtom);
    const clientFeatures = getClientFeatures(myAtom);
    console.log("feature", clientFeatures?.asyncExportDatasheet); // Dummy for testing
    const [managePermissionVisible, setManagePermissionVisible] =
      useState(false);

    const [selectedExportType, setSelectedExportType] = useState("csv");
    const [shouldApplyAdjustments, setShouldApplyAdjustments] = useState(false);
    const [isDataFilteredForExport, setIsDataFilteredForExport] =
      useState(true);
    const [needHiddenColumns, setNeedHiddenColumns] = useState(false);

    const cloneDatasheetRequest = useMutation(
      (data) => {
        return cloneDatasheet(accessToken, datasheetStore.datasheetId, data);
      },
      {
        onError: (error) => {
          toast.remove("cloneLoading");
          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description={error?.message || "Error occured"}
              />
            ),
            { position: "top-center" }
          );
        },
        onSuccess: () => {
          toast.remove("cloneLoading");
          toast.custom(
            () => (
              <EverHotToastMessage
                type="success"
                description={"Datasheet cloned!"}
              />
            ),
            { position: "top-center" }
          );
          datasheetStore.refetchDatasheetDetails();
          datasheetStore.viewByRefetchFn();
          datasheetStore.globalDatabookRefetch();
        },
      }
    );

    const archivedSheetHiddenButtons = {
      hardRefresh: true,
      managePermission: true,
      clone: true,
    };

    const dropdownButtons = [
      {
        key: "sheetDetails",
        label: "Sheet details",
        onClick: () => {
          const url = new URL(window.location);
          const searchParams = new URLSearchParams(url.search);
          // Set or update the query parameter with the provided key and value
          searchParams.set("detailsDatasheetId", datasheetStore.datasheetId);
          url.search = searchParams.toString();
          // Use history.replaceState to update the URL without reloading the page
          window.history.replaceState({}, "", url);
          datasheetStore.setDetailsDatasheetId(datasheetStore.datasheetId);
          datasheetStore.setDrawerVisibility(true);
        },
        icon: <InfoCircleIcon className="w-4 h-4 text-ever-base-content-mid" />,
        permission: null,
      },
      {
        key: "clone",
        onClick: async () => {
          toast.custom(
            () => (
              <EverHotToastMessage
                type="loading"
                description={"Cloning datasheet..."}
              />
            ),
            { position: "top-center", duration: Infinity, id: "cloneLoading" }
          );
          // TODO: Change this data object to new V2 payload
          const data = {
            databookId: datasheetStore.datasheetDetails.databook_id,
            databookName: datasheetStore.datasheetDetails.databook_name,
          };
          await cloneDatasheetRequest.mutate(data);
        },
        label: "Clone this sheet",
        icon: <CopyIcon className="w-4 h-4 text-ever-base-content-mid" />,
        permission: RBAC_ROLES.MANAGE_DATABOOK,
      },
      // {
      //   key: "move",
      //   label: "Move",
      //   icon: (
      //     <ReverseRightIcon className="w-4 h-4 text-ever-base-content-mid" />
      //   ),
      //   permission: null,
      // },
      // Separator visibility depends on the permissions of adjacent menu items.
      {
        label: "separator-1",
        value: "separator",
        permission: [
          RBAC_ROLES.MANAGE_ALL_ADMINS,
          RBAC_ROLES.MANAGE_DATASHEETPERMISSIONS,
        ],
      },
      {
        key: "hardRefresh",
        label: "Hard refresh",
        icon: <DataflowIcon className="w-4 h-4 text-ever-base-content-mid" />,
        onClick: () => {
          const modal = EverModal.confirm({
            title: "Hard refresh data in this sheet?",
            subtitle:
              "This will reconstruct this sheet with all data from the underlying source and could take a bit longer.",
            confirmationButtons: [
              <EverButton key="back" color="base" onClick={() => closeModal()}>
                Cancel
              </EverButton>,
              <EverButton
                key="submit"
                color="primary"
                onClick={() => {
                  setShowGenerateSheetModal(true);
                  handleUpdateDatasheet(null, true);
                  modal.destroy();
                }}
              >
                Yes, refresh
              </EverButton>,
            ],
          });
          const closeModal = () => {
            modal.destroy();
          };
        },
        permission: [RBAC_ROLES.MANAGE_ALL_ADMINS],
        disabled: datasheetStore.isSyncInProgress,
      },
      clientFeatures?.showDatasheetPermission && {
        key: "managePermission",
        label: "Manage permission",
        onClick: () => setManagePermissionVisible(true),
        disabled: datasheetStore.datasheetDetailsIsLoading,
        icon: <UserEditIcon className="w-4 h-4 text-ever-base-content-mid" />,
        permission: RBAC_ROLES.MANAGE_DATASHEETPERMISSIONS,
      },
      // Separator visibility depends on the permissions of adjacent menu items.
      {
        label: "separator-2",
        value: "separator",
        permission: [RBAC_ROLES.DELETE_DATASHEET],
      },
      {
        key: "delete",
        label: "Delete",
        icon: <Trash03Icon className="w-4 h-4 text-ever-error" />,
        className: "text-ever-error",
        onClick: () => {
          datasheetStore.setDatasheetNameToDelete(
            datasheetStore.currentDatasheetName
          );
          datasheetStore.setDatasheetIdToDelete(datasheetStore.datasheetId);
          datasheetStore.setIsDeletesheetModalVisible(true);
        },
        containerClassName: "hover:!bg-ever-error-lite",
        permission: RBAC_ROLES.DELETE_DATASHEET,
        disabled: datasheetStore.isSyncInProgress,
      },
    ].filter(Boolean);

    async function exportCSV(applyAdjustments) {
      const pivotConfigData = pivotConfig[activeKey] || {};
      const reqParams = {
        applyAdjustments,
        filters: datasheetStore.dsFilterExpressions[activeKey] || [], // TODO: Test filters
        pivotDetails:
          (pivotConfigData.isPivotMode ? pivotConfigData?.pivotData : {}) || {},
        exportType: selectedExportType,
      };
      const exportCSVToast = toast.custom(
        () => (
          <EverHotToastMessage
            type="loading"
            description={"Exporting datasheet..."}
          />
        ),
        { position: "top-center", duration: Infinity }
      );
      exportDatasheet(accessToken, datasheetStore.datasheetId, reqParams)
        .then((response) => {
          if (response.ok) {
            return response;
          } else {
            throw new Error("Request failed");
          }
        })
        .then((response) => response.blob())
        .then((blobby) => {
          const objectUrl = window.URL.createObjectURL(blobby);
          const anchor = document.createElement("a");
          anchor.href = objectUrl;
          anchor.download = `${datasheetStore.currentDatasheetName}.csv`;
          anchor.click();

          window.URL.revokeObjectURL(objectUrl);
          toast.remove(exportCSVToast);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="success"
                description={"Downloaded Successfully!!"}
              />
            ),
            { position: "top-center" }
          );
        })
        .catch(() => {
          toast.remove(exportCSVToast);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description={"Error while exporting datasheet"}
              />
            ),
            { position: "top-center" }
          );
        });
      return;
    }

    async function exportV2() {
      const pivotConfigData = pivotConfig[activeKey] || {};
      const reqParams = {
        applyAdjustments: shouldApplyAdjustments,
        filters: isDataFilteredForExport
          ? datasheetStore.dsFilterExpressions[activeKey] || []
          : [],
        pivotDetails:
          (pivotConfigData.isPivotMode ? pivotConfigData?.pivotData : {}) || {},
        exportType: selectedExportType,
        needHiddenColumns: needHiddenColumns,
        hiddenColumns: datasheetStore.hiddenColumns,
      };
      datasheetExportV2(accessToken, datasheetStore.datasheetId, reqParams)
        .then((response) => {
          if (response.ok) {
            toast.custom(
              () => (
                <EverHotToastMessage
                  type="success"
                  description={
                    "Your datasheet export has been initiated. We'll email the file once it's ready. You can also track its status in Settings → Activity Logs. "
                  }
                />
              ),
              { position: "top-center" }
            );
          } else {
            throw new Error("Request failed");
          }
        })
        .catch((error) => {
          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description={"Error while exporting datasheet"}
              />
            ),
            { position: "top-center" }
          );
          console.error("error", error);
        });
    }

    const renderMenuItem = (item) => (
      <Menu.Item
        key={item.key}
        onClick={() => {
          item.onClick && item.onClick();
        }}
        disabled={item.disabled}
        className={`!py-2 !px-3 flex gap-2 !h-8 ${item.containerClassName}`}
      >
        <div className={`flex gap-2 items-center ${item.className}`}>
          {item.icon}
          <EverTg.Caption
            className={twMerge("text-ever-base-content", item.className)}
          >
            {item.label}
          </EverTg.Caption>
        </div>
      </Menu.Item>
    );

    const menu = (
      <Menu>
        {hasPermissions(RBAC_ROLES.EXPORT_DATASHEET) &&
        datasheetStore.datasheetDetails?.does_user_has_co_permission &&
        clientFeatures?.asyncExportDatasheet ? (
          <NewExportMenu
            exportV2={exportV2}
            accessToken={accessToken}
            datasheetStore={datasheetStore}
            fileFormat={selectedExportType}
            setFileFormat={setSelectedExportType}
            t={t}
            shouldApplyAdjustments={shouldApplyAdjustments}
            setShouldApplyAdjustments={setShouldApplyAdjustments}
            activeKey={activeKey}
            isDataFilteredForExport={isDataFilteredForExport}
            setIsDataFilteredForExport={setIsDataFilteredForExport}
            needHiddenColumns={needHiddenColumns}
            setNeedHiddenColumns={setNeedHiddenColumns}
          />
        ) : (
          <Menu.SubMenu
            key="export"
            className="datasheet-export-sub-menu"
            title={
              <div className="flex items-center !h-8 gap-2">
                <UploadIcon className="w-4 h-4 text-ever-base-content-mid" />
                <EverTg.Caption className="!ml-0 text-ever-base-content ">
                  Export
                </EverTg.Caption>
              </div>
            }
            disabled={
              showGenerateSheetModal ||
              !datasheetStore.datasheetDetails?.does_user_has_co_permission
            }
          >
            <Menu.Item
              className="!h-8"
              key="preAdjustment"
              onClick={() => {
                sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.EXPORT_CSV, {
                  [ANALYTICS_PROPERTIES.EXPORT_TYPE]: t("PRE_ADJUSTMENT"),
                  [ANALYTICS_PROPERTIES.DATASHEET_NAME]:
                    datasheetStore.currentDatasheetName,
                });
                exportCSV(false);
              }}
            >
              <EverTg.Caption className="text-ever-base-content">
                Pre Adjustment
              </EverTg.Caption>
            </Menu.Item>
            <Menu.Item
              className="!h-8"
              key="postAdjustment"
              onClick={() => {
                sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.EXPORT_CSV, {
                  [ANALYTICS_PROPERTIES.EXPORT_TYPE]: t("POST_ADJUSTMENT"),
                  [ANALYTICS_PROPERTIES.DATASHEET_NAME]:
                    datasheetStore.currentDatasheetName,
                });
                exportCSV(true);
              }}
            >
              <EverTg.Caption className="text-ever-base-content">
                Post Adjustment
              </EverTg.Caption>
            </Menu.Item>
          </Menu.SubMenu>
        )}
        {dropdownButtons
          .map((item, idx) => {
            if (
              datasheetStore.datasheetDetails?.is_archived &&
              (archivedSheetHiddenButtons[item.key] ||
                item.label === "separator-1")
            ) {
              return undefined;
            }
            if (item.permission && !hasPermissions(item.permission)) {
              return undefined;
            }
            if (
              viewBy === DATASHEET_VIEW_BY.COMMISSION_PLAN &&
              item.key === "clone"
            ) {
              return undefined;
            }
            if (item.value === "separator") {
              return (
                <div className="py-1" key={`divider-${idx}`}>
                  <EverDivider />
                </div>
              );
            }
            return renderMenuItem(item);
          })
          .filter((value) => value !== undefined)}
      </Menu>
    );
    return (
      <>
        <Dropdown overlay={menu} trigger={["click"]}>
          <EverButton.Icon
            color="base"
            type="filled"
            icon={<DotsVerticalIcon />}
            size="small"
          />
        </Dropdown>
        {managePermissionVisible && (
          <ManagePermissionDrawer
            datasheetStore={datasheetStore}
            visible={managePermissionVisible}
            setDrawerOpen={setManagePermissionVisible}
          />
        )}
      </>
    );
  }
);

const NewExportMenu = ({
  exportV2,
  accessToken,
  datasheetStore,
  fileFormat,
  setFileFormat,
  t,
  shouldApplyAdjustments,
  setShouldApplyAdjustments,
  activeKey,
  isDataFilteredForExport,
  setIsDataFilteredForExport,
  needHiddenColumns,
  setNeedHiddenColumns,
}) => {
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);

  return (
    <Menu.Item key="export" className="datasheet-export-sub-menu">
      <EverPopover
        open={isExportModalOpen}
        className="z-1000 border-solid border border-ever-base-400 rounded-lg w-44 p-2"
        align="start"
        side="left"
        sideOffset={15}
        alignOffset={-12}
        showArrow={false}
        showCloseIcon={false}
        onOpenChange={setIsExportModalOpen}
        content={
          <div className="p-2">
            <div className="mb-4">
              <h3 className="text-ever-base-content mb-4">Adjustment</h3>
              <EverRadio.Group
                value={shouldApplyAdjustments ? "post" : "pre"}
                onChange={(e) =>
                  setShouldApplyAdjustments(e.target.value === "post")
                }
              >
                <Space direction="vertical" className="w-full">
                  <EverRadio value="pre" className="items-center">
                    Pre adjustment
                  </EverRadio>
                  <EverRadio value="post" className="items-center">
                    Post adjustment
                  </EverRadio>
                </Space>
              </EverRadio.Group>
              <EverDivider className="my-3" type="horizontal" />
            </div>

            {datasheetStore.dsFilterExpressions[activeKey]?.length > 0 && (
              <div className="mb-4">
                <h3 className="text-ever-base-content mb-4">Filters</h3>
                <EverRadio.Group
                  value={isDataFilteredForExport ? "filtered" : "all"}
                  onChange={(e) => {
                    setIsDataFilteredForExport(e.target.value === "filtered");
                  }}
                >
                  <Space direction="vertical" className="w-full">
                    <EverRadio value="filtered" className="items-center">
                      Filtered Data
                    </EverRadio>
                    <EverRadio value="all" className="items-center">
                      All Data
                    </EverRadio>
                  </Space>
                </EverRadio.Group>
                <EverDivider className="my-3" type="horizontal" />
              </div>
            )}

            {datasheetStore.hasHiddenColumns && (
              <div className="mb-4">
                <h3 className="text-ever-base-content mb-4">Columns</h3>
                <EverRadio.Group
                  value={needHiddenColumns ? "all" : "selected"}
                  onChange={(e) =>
                    setNeedHiddenColumns(e.target.value === "all")
                  }
                >
                  <Space direction="vertical" className="w-full">
                    <EverRadio value="selected" className="items-center">
                      Selected
                    </EverRadio>
                    <EverRadio value="all" className="items-center">
                      All
                    </EverRadio>
                  </Space>
                </EverRadio.Group>
                <EverDivider className="my-3" type="horizontal" />
              </div>
            )}

            <div className="mb-4">
              <h3 className="text-ever-base-content mb-4">File format</h3>
              <EverRadio.Group
                value={fileFormat}
                onChange={(e) => setFileFormat(e.target.value)}
              >
                <Space direction="vertical" className="w-full">
                  <EverRadio value="csv" className="items-center">
                    CSV
                  </EverRadio>
                  <EverRadio value="xlsx" className="items-center">
                    XLSX
                  </EverRadio>
                  <EverRadio value="xls" className="items-center">
                    XLS
                  </EverRadio>
                </Space>
              </EverRadio.Group>
            </div>

            <EverButton
              block
              onClick={() => {
                sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.EXPORT_CSV, {
                  [ANALYTICS_PROPERTIES.EXPORT_TYPE]: shouldApplyAdjustments
                    ? t("POST_ADJUSTMENT")
                    : t("PRE_ADJUSTMENT"),
                  [ANALYTICS_PROPERTIES.DATASHEET_NAME]:
                    datasheetStore.currentDatasheetName,
                });
                exportV2();
                setIsExportModalOpen(false);
              }}
              size="small"
            >
              Download
            </EverButton>
          </div>
        }
      >
        <div className="flex items-center !h-8 gap-2 p-3 cursor-pointer hover:bg-ever-base-100">
          <UploadIcon className="w-4 h-4 text-ever-base-content-mid" />
          <EverTg.Caption className="!ml-0 text-ever-base-content">
            Export
          </EverTg.Caption>
          <ChevronRightIcon className="w-4 h-4 text-ever-base-content-mid ml-auto" />
        </div>
      </EverPopover>
    </Menu.Item>
  );
};
