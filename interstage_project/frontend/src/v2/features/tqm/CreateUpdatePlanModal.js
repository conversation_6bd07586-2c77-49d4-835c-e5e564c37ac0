import {
  AlertCircleIcon,
  CheckCircleIcon,
  PlusIcon,
  InfoCircleIcon,
  AlertTriangleIcon,
} from "@everstage/evericons/outlined";
import { XCircleIcon } from "@everstage/evericons/solid";
import { parseISO, startOfDay, endOfDay, format } from "date-fns";
import { observer } from "mobx-react";
import React, { Fragment, useEffect, useState, useCallback } from "react";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverBadge,
  EverButton,
  EverForm,
  EverInput,
  EverModal,
  EverSelect,
  EverDatePicker,
  EverRadio,
  EverHotToastMessage,
  EverTg,
  EverTooltip,
  EverCollapse,
  toast,
} from "~/v2/components";

import {
  createTerritoryPlan,
  getDatabooks,
  getTerritoryPlanDetails,
  updateTerritoryPlan,
} from "./restApi";
import { parseUTC } from "../commission-canvas-modified/manage-users/utils";

const { Caption, Heading2, Heading4 } = EverTg;

const formatDateToRequestFormat = (date) => {
  return format(date, "dd-MMM-yyyy");
};

// Data source section types and their requirements
const DATA_SOURCE_SECTIONS = [
  { key: "accounts", label: "Accounts", required: true, maxSources: 1 },
  {
    key: "users",
    label: "Go-to-market roster",
    required: true,
    maxSources: 1,
  },
  { key: "hierarchy", label: "Hierarchy", required: true, maxSources: 5 },
  { key: "others", label: "Others", required: false, maxSources: 5 },
];

const CreateUpdatePlanModal = observer(
  ({
    open = false,
    setOpen,
    onPlanCreated,
    onPlanUpdated,
    editMode = false,
    planId = null,
  }) => {
    const { accessToken } = useAuthStore();
    const [isModalOpen, setIsModalOpen] = useState(open);
    const [form] = EverForm.useForm();
    const [isLoading, setIsLoading] = useState(false);
    const [databooks, setDatabooks] = useState([]);
    const [datasheets, setDatasheets] = useState({});
    const [isDirty, setIsDirty] = useState(false);
    const [activeAccordion, setActiveAccordion] = useState("accounts");
    const [validationStates, setValidationStates] = useState({});
    const [showError, setShowError] = useState(false);

    // Define fetchPlanDetails with useCallback to avoid dependency cycle
    const fetchPlanDetails = useCallback(async () => {
      setIsLoading(true);
      try {
        if (!planId) {
          throw new Error("Plan ID is required for editing");
        }

        const data = await getTerritoryPlanDetails(accessToken, planId);

        // Parse ISO strings to Date objects
        const startDate = data.effectiveStartDate
          ? parseUTC(data.effectiveStartDate)
          : null;
        const endDate = data.effectiveEndDate
          ? parseUTC(data.effectiveEndDate)
          : null;

        // Group data sources by tag
        const dataSourcesByTag = {};

        for (const source of data.dataSources) {
          const tag = source.tag || "others";
          if (!dataSourcesByTag[tag]) {
            dataSourcesByTag[tag] = [];
          }

          dataSourcesByTag[tag].push({
            databook: source.databookId,
            datasheet: source.datasheetId,
            syncOption: source.syncOption || "choose_custom_date",
            syncDate: source.syncDate ? parseISO(source.syncDate) : undefined,
          });
        }

        // Set form values with parsed dates and grouped data sources
        const formValues = {
          planName: data.planName,
          fiscalYear: [startDate, endDate],
        };
        for (const [tag, sources] of Object.entries(dataSourcesByTag)) {
          formValues[tag] = sources;
        }
        form.setFieldsValue(formValues);

        setIsDirty(false);
      } catch (error) {
        toast.custom(() => (
          <EverHotToastMessage
            type="error"
            description={error.message || "Failed to fetch plan details"}
          />
        ));
      } finally {
        setIsLoading(false);
      }
    }, [accessToken, form, planId]);

    // Define fetchDatabooks with useCallback to avoid dependency cycle
    const fetchDatabooks = useCallback(async () => {
      try {
        const data = await getDatabooks(accessToken);
        setDatabooks(data);

        // Pre-populate datasheets for each databook
        const datasheetsByDatabook = {};
        for (const databook of data) {
          datasheetsByDatabook[databook.id] = databook.datasheets || [];
        }
        setDatasheets(datasheetsByDatabook);
      } catch {
        toast.custom(() => (
          <EverHotToastMessage
            type="error"
            description="Failed to fetch databooks"
          />
        ));
      }
    }, [accessToken]);

    // Sync internal state with external control
    useEffect(() => {
      setIsModalOpen(open);
      setActiveAccordion("accounts");

      // Initialize form state for all sections
      if (open) {
        const initialValues = {};
        for (const section of DATA_SOURCE_SECTIONS) {
          initialValues[section.key] = section.required ? [{}] : [];
        }
        form.setFieldsValue(initialValues);
      }
    }, [open, form]);

    useEffect(() => {
      if (isModalOpen && editMode && planId) {
        fetchPlanDetails();
      }
    }, [isModalOpen, editMode, planId, fetchPlanDetails]);

    useEffect(() => {
      if (isModalOpen) {
        fetchDatabooks();
      }
    }, [isModalOpen, fetchDatabooks]);

    const handleCancel = () => {
      if (setOpen) {
        setOpen(false);
      }
      setIsModalOpen(false);
      form.resetFields();
      setValidationStates({});
      setIsDirty(false);
      setShowError(false);
    };

    const transformFormData = (values) => {
      // Ensure all form data is properly captured
      const formValues = form.getFieldsValue(true);

      // Merge with the values argument to ensure we have complete data
      const completeValues = { ...formValues, ...values };

      const startDate = formatDateToRequestFormat(
        startOfDay(completeValues.fiscalYear[0])
      );
      const endDate = formatDateToRequestFormat(
        endOfDay(completeValues.fiscalYear[1])
      );

      // Flatten all data sources from different sections
      const allDataSources = [];
      for (const section of DATA_SOURCE_SECTIONS) {
        const sources = completeValues[section.key] || [];
        for (const source of sources) {
          if (source && source.databook && source.datasheet) {
            allDataSources.push({
              databookId: source.databook,
              datasheetId: source.datasheet,
              tag: section.key,
              syncOption: "choose_custom_date",
              syncDate: format(source.syncDate, "yyyy-MM-dd"),
            });
          }
        }
      }

      return {
        name: completeValues.planName,
        effectiveStartDate: startDate,
        effectiveEndDate: endDate,
        planStage: "Draft",
        dataSources: allDataSources,
      };
    };

    const handleAccordionChange = (key) => {
      // If clicking the same accordion, close it
      if (activeAccordion === key) {
        setActiveAccordion(null);
      } else {
        setActiveAccordion(key);
      }
    };

    const checkInputFields = (section) => {
      const fields = form.getFieldValue(section.key) || [];
      return fields.every(
        (field) => field.databook && field.datasheet && field.syncDate
      );
    };

    const updateValidationState = (sectionKey) => {
      const isValid = checkInputFields({ key: sectionKey });
      setValidationStates((prev) => ({
        ...prev,
        [sectionKey]: isValid,
      }));
    };

    const handleFieldChange = (sectionKey) => {
      setIsDirty(true);
      //form.validateFields();
      updateValidationState(sectionKey);
    };

    // Add a function to validate all sections regardless of accordion state
    const validateAllSections = () => {
      let allValid = true;
      for (const section of DATA_SOURCE_SECTIONS) {
        const sectionValid = section.required
          ? checkInputFields(section)
          : true;
        setValidationStates((prev) => ({
          ...prev,
          [section.key]: sectionValid,
        }));
        if (!sectionValid) {
          allValid = false;
        }
      }
      return allValid;
    };

    const renderDataSourceSection = (section) => {
      const sourcesCount = form.getFieldValue(section.key)?.length || 0;
      const isValid = validationStates[section.key] === true;
      return (
        <>
          {showError && (section.required || sourcesCount > 0) && !isValid && (
            <div className="mb-4 p-2 bg-red-50 text-red-500 border border-red-200 rounded-md flex items-center">
              <AlertTriangleIcon className="w-4 h-4 mr-2" />
              <span className="text-sm font-medium">
                Complete required fields to continue
              </span>
            </div>
          )}
          {!section.required && sourcesCount === 0 && (
            <div className="flex flex-col items-center justify-center py-8 gap-1">
              <div className="text-lg font-semibold text-ever-base-content-mid">
                No additional datasets added
              </div>
              <div className="text-base text-ever-base-content-mid">
                Add datasets if required
              </div>
            </div>
          )}
          <EverForm.List
            name={section.key}
            initialValue={section.required ? [{}] : []}
          >
            {(fields, { remove }) => (
              <>
                {fields.map((field, index) => {
                  const databookValue = form.getFieldValue([
                    section.key,
                    index,
                    "databook",
                  ]);

                  return (
                    <div
                      key={`${section.key}-${field.key}`}
                      className="pt-2 pr-2 pl-4 border rounded mb-2"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 flex flex-col">
                          <div className="flex gap-4">
                            <EverForm.Item
                              {...field}
                              label="Databook"
                              name={[field.name, "databook"]}
                              className="flex-1"
                              rules={[
                                {
                                  required: true,
                                  message: "",
                                },
                              ]}
                              validateStatus=""
                              help=""
                            >
                              <EverSelect
                                placeholder="Select Databook"
                                size="small"
                                disabled={editMode}
                                onChange={() => {
                                  const currentValues = form.getFieldValue(
                                    section.key
                                  );
                                  currentValues[index] = {
                                    ...currentValues[index],
                                    datasheet: undefined,
                                  };
                                  form.setFieldsValue({
                                    [section.key]: currentValues,
                                  });
                                  handleFieldChange(section.key);
                                }}
                              >
                                {databooks.map((book) => (
                                  <EverSelect.Option
                                    key={`${section.key}-${field.key}-${book.id}`}
                                    value={book.id}
                                  >
                                    {book.name}
                                  </EverSelect.Option>
                                ))}
                              </EverSelect>
                            </EverForm.Item>

                            <EverForm.Item
                              {...field}
                              label="Datasheet"
                              name={[field.name, "datasheet"]}
                              className="flex-1"
                              rules={[
                                {
                                  required: true,
                                  message: "",
                                },
                              ]}
                              validateStatus=""
                              help=""
                            >
                              <EverSelect
                                placeholder="Select Datasheet"
                                size="small"
                                disabled={!databookValue || editMode}
                                onChange={() => handleFieldChange(section.key)}
                              >
                                {(datasheets[databookValue] || []).map(
                                  (sheet) => (
                                    <EverSelect.Option
                                      key={`${section.key}-${field.key}-${sheet.datasheet_id}`}
                                      value={sheet.datasheet_id}
                                    >
                                      {sheet.name}
                                    </EverSelect.Option>
                                  )
                                )}
                              </EverSelect>
                            </EverForm.Item>
                          </div>

                          <EverForm.Item
                            {...field}
                            name={[field.name, "syncOption"]}
                            label="Use data that's"
                            initialValue="choose_custom_date"
                          >
                            <div className="flex items-center">
                              <EverForm.Item
                                {...field}
                                name={[field.name, "syncOption"]}
                                noStyle
                              >
                                <EverRadio.Group
                                  value="choose_custom_date"
                                  className="flex items-center"
                                >
                                  <div className="flex items-center mr-4">
                                    <EverRadio
                                      value="latest-sync-data"
                                      disabled={true}
                                    >
                                      Always up-to-date
                                    </EverRadio>
                                    <EverTooltip
                                      title="Automatically refreshes after every sync"
                                      placement="bottom"
                                    >
                                      <InfoCircleIcon className="w-4 h-4 text-ever-base-content-mid" />
                                    </EverTooltip>
                                  </div>
                                  <div className="flex items-center mr-4">
                                    <EverRadio
                                      value="choose_custom_date"
                                      disabled={editMode}
                                    >
                                      Snapshot on
                                    </EverRadio>
                                    <EverTooltip
                                      title="Snapshot of the data as it existed on the chosen date. Later syncs won't change it"
                                      placement="bottom"
                                    >
                                      <InfoCircleIcon className="w-4 h-4 text-ever-base-content-mid" />
                                    </EverTooltip>
                                  </div>
                                </EverRadio.Group>
                              </EverForm.Item>
                              <EverForm.Item
                                {...field}
                                name={[field.name, "syncDate"]}
                                noStyle
                                rules={[
                                  {
                                    required: true,
                                    message: "",
                                  },
                                ]}
                                validateStatus=""
                                help=""
                              >
                                <EverDatePicker
                                  className="w-[200px]"
                                  size="small"
                                  disabled={editMode}
                                  onChange={() =>
                                    handleFieldChange(section.key)
                                  }
                                  format="MMM DD, YYYY"
                                />
                              </EverForm.Item>
                            </div>
                          </EverForm.Item>
                        </div>

                        {!editMode &&
                          (!section.required || fields.length > 1) && (
                            <XCircleIcon
                              className="w-4 h-4 cursor-pointer text-ever-base-content-mid"
                              onClick={() => {
                                remove(field.name);
                                updateValidationState(section.key);
                              }}
                            />
                          )}
                      </div>
                    </div>
                  );
                })}

                {fields.length < section.maxSources && (
                  <div className="flex justify-center">
                    <EverButton
                      type="ghost"
                      color="base"
                      size="small"
                      onClick={() => {
                        // Get current values
                        const currentValues =
                          form.getFieldValue(section.key) || [];
                        // Add new empty object
                        const newValues = [...currentValues, {}];
                        // Set the new values
                        form.setFieldsValue({
                          [section.key]: newValues,
                        });
                        const sourcesCount =
                          form.getFieldValue(section.key)?.length || 0;
                        const valid = section.required || sourcesCount > 0;
                        const sectionValid = valid
                          ? checkInputFields(section)
                          : true;
                        // Update validation state
                        setValidationStates((prev) => ({
                          ...prev,
                          [section.key]: sectionValid,
                        }));
                      }}
                      className="mt-2 mb-0 rounded-md"
                      disabled={editMode}
                      prependIcon={<PlusIcon className="w-4 h-4" />}
                    >
                      Add dataset
                    </EverButton>
                  </div>
                )}
              </>
            )}
          </EverForm.List>
        </>
      );
    };

    const renderAccordionHeader = (section) => {
      const sourcesCount = form.getFieldValue(section.key)?.length || 0;
      const isInputFieldsValid = checkInputFields(section);
      //const isAccordionActive = activeAccordion === section.key;
      return (
        <div
          className="flex items-center justify-between w-full cursor-pointer"
          onClick={() => handleAccordionChange(section.key)}
        >
          <div className="flex items-center gap-2">
            <div className="text-sm font-medium">{section.label}</div>
            <EverBadge
              title={section.required ? "Required" : "Optional"}
              type="base"
              className="rounded"
            />
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs text-ever-base-content-mid">
              {isInputFieldsValid && sourcesCount > 0
                ? `${sourcesCount} ${sourcesCount <= 1 ? "Source" : "Sources"}`
                : showError && (section.required || sourcesCount > 0)
                ? "Error"
                : null}
            </span>
            {/* Show validation symbols only for required sections or optional sections with sources */}
            {section.required ? (
              <div className="flex items-center gap-1">
                {isInputFieldsValid && sourcesCount > 0 ? (
                  <CheckCircleIcon className="w-4 h-4 text-green-500" />
                ) : showError ? (
                  <AlertCircleIcon className="w-4 h-4 text-red-500" />
                ) : null}
              </div>
            ) : sourcesCount > 0 ? (
              <div className="flex items-center gap-1">
                {isInputFieldsValid ? (
                  <CheckCircleIcon className="w-4 h-4 text-green-500" />
                ) : showError ? (
                  <AlertCircleIcon className="w-4 h-4 text-red-500" />
                ) : null}
              </div>
            ) : null}
          </div>
        </div>
      );
    };

    const onFinish = async (values) => {
      setShowError(true);

      // First check basic form fields
      if (!values.planName?.trim()) {
        toast.custom(() => (
          <EverHotToastMessage
            type="error"
            description="This field is required!"
          />
        ));
        return;
      }

      if (!values.fiscalYear?.[0] || !values.fiscalYear?.[1]) {
        toast.custom(() => (
          <EverHotToastMessage
            type="error"
            description="This field is required!"
          />
        ));
        return;
      }

      // Validate all sections regardless of which accordion is open
      if (!validateAllSections()) {
        // Find first invalid section to open its accordion
        for (const section of DATA_SOURCE_SECTIONS) {
          if (section.required && !validationStates[section.key]) {
            setActiveAccordion(section.key);
            return;
          }
        }
      }

      setIsLoading(true);
      const toastId = toast.custom(() => (
        <EverHotToastMessage
          type="loading"
          description={editMode ? "Updating plan..." : "Creating plan..."}
        />
      ));
      try {
        // Ensure we're getting all form data by calling form.getFieldsValue directly
        const allFormValues = form.getFieldsValue(true);
        const payload = transformFormData(allFormValues);

        // Verify payload has data sources
        if (payload.dataSources.length === 0) {
          toast.remove(toastId);
          toast.custom(() => (
            <EverHotToastMessage
              type="error"
              description="No valid data sources provided. Please add at least one data source to each required section."
            />
          ));
          setIsLoading(false);
          return;
        }

        const data = editMode
          ? await updateTerritoryPlan(accessToken, planId, payload)
          : await createTerritoryPlan(accessToken, payload);

        if (!editMode && data.planId) {
          onPlanCreated?.(data.planId);
        }
        if (editMode) {
          onPlanUpdated?.(planId);
        }

        toast.remove(toastId);
        toast.custom(() => (
          <EverHotToastMessage
            type="success"
            description={
              editMode
                ? "Plan updated successfully"
                : "Plan created successfully"
            }
          />
        ));
        handleCancel();
      } catch (error) {
        toast.remove(toastId);
        toast.custom(() => (
          <EverHotToastMessage
            type="error"
            description={error.message || "Failed to save plan"}
          />
        ));
      } finally {
        setIsLoading(false);
        setIsDirty(false);
      }
    };

    const onFinishFailed = ({ errorFields }) => {
      setShowError(true);

      // If no missing sections, check error fields
      if (errorFields) {
        switch (errorFields[0].name[0]) {
          case "users": {
            setActiveAccordion("users");

            break;
          }
          case "others": {
            setActiveAccordion("others");

            break;
          }
          case "hierarchy": {
            setActiveAccordion("hierarchy");

            break;
          }
          default: {
            setActiveAccordion("accounts");
          }
        }
      }
    };

    return (
      <Fragment>
        <EverModal
          title={
            <Heading2>{editMode ? "Edit Plan" : "Create New Plan"}</Heading2>
          }
          visible={isModalOpen}
          onCancel={handleCancel}
          footer={
            <div className="flex items-center justify-end gap-2 px-4 h-8">
              <EverButton type="text" color="base" onClick={handleCancel}>
                Cancel
              </EverButton>
              <EverButton
                type="filled"
                size="small"
                color="primary"
                htmlType="submit"
                form="create-plan-form"
                disabled={isLoading || !isDirty}
                loading={isLoading}
              >
                {editMode ? "Update" : "Create"}
              </EverButton>
            </div>
          }
          width="820px"
          destroyOnClose
          maskClosable={false}
        >
          <EverForm
            id="create-plan-form"
            form={form}
            layout="vertical"
            onFinish={onFinish}
            onValuesChange={() => setIsDirty(true)}
            onFinishFailed={onFinishFailed}
          >
            <div className="mb-2">
              <div className="flex gap-4 mb-2">
                <EverForm.Item
                  name="planName"
                  label="Plan name"
                  className="flex-1"
                  rules={[
                    {
                      required: true,
                      message: "This field is required!",
                      validator: (_, value) => {
                        if (value && value.trim() !== "") {
                          return Promise.resolve();
                        }
                        return Promise.reject(
                          new Error("This field is required!")
                        );
                      },
                    },
                  ]}
                >
                  <EverInput
                    placeholder="Enter Plan Name"
                    size="small"
                    className="bg-ever-base-100"
                  />
                </EverForm.Item>

                <EverForm.Item
                  name="fiscalYear"
                  label="Plan period"
                  className="flex-1"
                  rules={[
                    { required: true, message: "This field is required!" },
                  ]}
                >
                  <EverDatePicker.RangePicker
                    className="w-full"
                    placeholder={["Start Date", "End Date"]}
                    format="MMM dd, yyyy"
                    size="small"
                  />
                </EverForm.Item>
              </div>
            </div>
            <div className="border-t border-ever-base-300"></div>
            <div className="mb-4 mt-4">
              <div className="flex flex-col gap-[2px]">
                <Heading4>Data sources</Heading4>
                <Caption className="text-ever-base-content-mid">
                  Add and map relevant datasets
                </Caption>
              </div>

              <EverCollapse
                className="mt-4 border-none bg-transparent"
                destroyInactivePanel={false}
                activeKey={activeAccordion}
                onChange={(keys) => {
                  // If all panels are closed, open accounts by default
                  if (keys.length === 0) {
                    setActiveAccordion("accounts");
                  } else {
                    setActiveAccordion(keys.at(-1));
                  }
                }}
              >
                {DATA_SOURCE_SECTIONS.map((section) => {
                  return (
                    <EverCollapse.Panel
                      key={section.key}
                      header={renderAccordionHeader(section)}
                      className={`border mb-2`}
                    >
                      <div className="px-1 py-1">
                        {renderDataSourceSection(section)}
                      </div>
                    </EverCollapse.Panel>
                  );
                })}
              </EverCollapse>
            </div>
          </EverForm>
        </EverModal>
      </Fragment>
    );
  }
);

export default CreateUpdatePlanModal;
