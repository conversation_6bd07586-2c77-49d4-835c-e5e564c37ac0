import { TrashLottie } from "@everstage/evericons/lotties";
import {
  SettingsIcon,
  DotsVerticalIcon,
  Trash03Icon,
  CalendarIcon,
  EditPencilAltIcon,
} from "@everstage/evericons/outlined";
import { CopyIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu } from "antd";
import PropTypes from "prop-types";
import React, { useState, useEffect, useRef, useMemo } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useSetRecoilState } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { breadcrumbAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverTg,
  EverButton,
  EverPopover,
  EverModal,
  EverHotToastMessage,
  toast,
  EverLoader,
  IconButton,
  EverDivider,
  EverFormatter,
} from "~/v2/components";

import ConfigurePlanForm from "../ConfigurePlanForm";
import CreateUpdatePlanModal from "../CreateUpdatePlanModal.js";
import PlanStage from "../PlanStage";
import {
  copyTerritoryPlan,
  deleteTerritoryPlan,
  getTerritoryPlanDetails,
} from "../restApi";

const TerritoryPlanDetails = () => {
  const { planId } = useParams();
  const [embedData, setEmbedData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const { accessToken } = useAuthStore();
  const { hasPermissions } = useUserPermissionStore();
  const employeeStore = useEmployeeStore();
  const setBreadcrumbName = useSetRecoilState(breadcrumbAtom);
  const navigate = useNavigate();
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const sigmaIframeRef = useRef(null);
  // Check if user has explorer permissions
  const canManageAllAdmins = useMemo(
    () => hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS),
    [hasPermissions]
  );

  const handleDelete = async (planId) => {
    try {
      await deleteTerritoryPlan(accessToken, planId);

      // Get the current path that needs to be removed
      const pathToRemove = `/planning/${planId}`;

      // Create a new history state array excluding the deleted plan URL
      const newStates = [];
      const currentLength = window.history.length;

      // Move back through history and rebuild it without the deleted plan URL
      for (let i = 0; i < currentLength; i++) {
        if (window.location.pathname !== pathToRemove) {
          newStates.unshift(window.location.href);
        }
        window.history.back();
      }

      // Rebuild history without the deleted plan URL
      for (const url of newStates) {
        window.history.pushState(null, "", url);
      }

      // Finally navigate to the list page
      window.location.href = "/planning";
    } catch (error) {
      console.error("Error deleting plan:", error);
    }
  };

  const handleCopy = async (planId) => {
    const toastId = toast.custom(() => (
      <EverHotToastMessage type="loading" description={"Copying plan..."} />
    ));
    try {
      const data = await copyTerritoryPlan(accessToken, planId);
      toast.remove(toastId);
      if (data) {
        toast.custom(() => (
          <EverHotToastMessage
            type="success"
            description={`${embedData?.planName} cloned successfully`}
          />
        ));
      }
      navigate(`/planning/${data.planId}`);
    } catch (error) {
      toast.remove(toastId);
      console.error("Error copying plan:", error);
    }
  };

  useEffect(() => {
    const fetchEmbedData = async () => {
      if (!planId) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const data = await getTerritoryPlanDetails(accessToken, planId);
        setEmbedData(data);
        setError(null);
      } catch (error_) {
        console.error("Error fetching plan:", {
          error: error_,
          message: error_.message,
          stack: error_.stack,
        });
        setError(error_);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEmbedData();
  }, [planId, accessToken]);

  useEffect(() => {
    setBreadcrumbName([
      {
        index: 0,
        name: "All Plans",
        title: "All Plans",
        url: "/planning",
      },
      {
        index: 1,
        name: embedData?.planName || "",
        title: embedData?.planName || "",
        disabled: true,
      },
    ]);
  }, [setBreadcrumbName, embedData, planId]);

  useEffect(() => {
    employeeStore.refetch();
  }, [employeeStore]);

  const planName = useMemo(() => {
    let _name = embedData?.planName || "";
    _name = _name.length > 65 ? _name.slice(0, 62) + "..." : _name;
    _name = _name.charAt(0).toUpperCase() + _name.slice(1);
    return _name;
  }, [embedData?.planName]);

  // Use the ConfigurePlanForm component instead of implementing the same functionality here
  const implementPopoverContent = (
    <div className="py-2">
      <ConfigurePlanForm tplanId={planId} />
    </div>
  );

  // const postMessageToSigma = (message) => {
  //   try {
  //     sigmaIframeRef.current.contentWindow.postMessage(
  //       message,
  //       "https://app.sigmacomputing.com"
  //     );
  //     console.log("Message sent successfully");
  //   } catch (error) {
  //     console.error("Error sending message to Sigma iframe:", error);
  //   }
  // };

  // const handleIframeLoad = () => {
  //   if (canManageAllAdmins) {
  //     const handleSigmaMessage = (event) => {
  //       if (
  //         event.source === sigmaIframeRef.current.contentWindow &&
  //         event.origin === "https://app.sigmacomputing.com"
  //       ) {
  //         if (event.data && event.data.type === "workbook:loaded") {
  //           // postMessageToSigma({
  //           //   type: "workbook:mode:update",
  //           //   mode: "explore",
  //           // });
  //           window.removeEventListener("message", handleSigmaMessage);
  //         }
  //       }
  //     };
  //     window.addEventListener("message", handleSigmaMessage);
  //   }
  // };

  if (!planId) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p className="text-gray-500">
          No plan selected. Please select a plan to view details.
        </p>
      </div>
    );
  }

  if (isLoading) {
    return <EverLoader />;
  }

  if (error) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p className="text-red-500">
          Failed to load plan details. Please try again.
        </p>
      </div>
    );
  }

  const handleMenuClick = ({ key }) => {
    if (key === "copy") {
      handleCopy(planId);
    } else if (key === "delete") {
      setShowDeleteConfirm(true);
    }
  };

  const menu = (
    <Menu onClick={handleMenuClick}>
      <Menu.Item key="copy">
        <div className="flex gap-2 items-center">
          <CopyIcon className="w-4 h-4 text-ever-base-content-mid" />
          <span>Clone</span>
        </div>
      </Menu.Item>
      <Menu.Item
        key="delete"
        className="flex gap-2 items-center hover:!bg-ever-error-lite"
      >
        <Trash03Icon className="w-4 h-4 text-ever-error" />
        <EverTg.Text className="text-ever-error-lite-content">
          Delete
        </EverTg.Text>
      </Menu.Item>
    </Menu>
  );

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between bg-ever-base-50 px-6 border-b border-ever-base-300">
        <div className="flex items-center gap-3 pb-2">
          <EverTg.Heading2>{planName}</EverTg.Heading2>
          <EverDivider type="vertical" />
          <div className="flex flex-row font-xs items-center text-ever-base-content-mid gap-1.5">
            <CalendarIcon className="w-4 h-4 flex-shrink-0" />
            <span className="truncate text-sm">
              <EverFormatter.Date date={embedData?.effectiveStartDate} /> -{" "}
              <EverFormatter.Date date={embedData?.effectiveEndDate} />
            </span>
          </div>
          <EverDivider type="vertical" />
          <PlanStage stage={embedData?.planStage} />
        </div>

        {canManageAllAdmins && (
          <div className="flex items-center gap-2 pb-3">
            <EverButton
              type="ghost"
              color="base"
              onClick={() => setShowEditModal(true)}
              size="small"
              danger
              prependIcon={
                <EditPencilAltIcon className="w-4 h-4 text-ever-base-content-mid" />
              }
            >
              Edit
            </EverButton>

            <EverPopover
              open={isPopoverOpen}
              onOpenChange={setIsPopoverOpen}
              content={implementPopoverContent}
              onClose={() => setIsPopoverOpen(false)}
            >
              <EverButton
                type="ghost"
                color="base"
                onClick={() => setIsPopoverOpen(true)}
                danger
                size="small"
                prependIcon={
                  <SettingsIcon className="w-4 h-4 text-ever-base-content-mid" />
                }
              >
                Configure
              </EverButton>
            </EverPopover>

            <Dropdown trigger={["click"]} overlay={menu}>
              <IconButton
                type="ghost"
                color="base"
                size="small"
                icon={
                  <DotsVerticalIcon className="text-ever-base-content-mid" />
                }
                danger
              />
            </Dropdown>
          </div>
        )}
      </div>

      {/* <div className="space-x-4 flex flex-row">
        <div className="text-sm py-2">
          <EverTg.Text className="text-gray-400">Fiscal Year: </EverTg.Text>
          <EverTg.Text>
            {format(parseISO(embedData?.effectiveStartDate), "MMM dd, yyyy")}
            {" - "}
            {format(parseISO(embedData?.effectiveEndDate), "MMM dd, yyyy")}
          </EverTg.Text>
        </div>
      </div> */}
      <div className="flex-grow w-full h-0">
        {embedData?.embedUrl ? (
          <iframe
            ref={sigmaIframeRef}
            className="w-full h-full"
            src={embedData.embedUrl}
            title="Territory Plan Embed"
            sandbox="allow-same-origin allow-scripts allow-forms"
            loading="lazy"
            // onLoad={handleIframeLoad}
          ></iframe>
        ) : (
          <div className="flex h-full items-center justify-center">
            <p className="text-gray-500">
              No embed URL available for this plan
            </p>
          </div>
        )}
      </div>

      <EverModal.Confirm
        visible={showDeleteConfirm}
        iconContainerClasses="border-ever-error border-solid border bg-transparent"
        icon={
          <TrashLottie
            autoplay
            loop
            className="w-8 h-8 text-ever-error-content"
          />
        }
        type="error"
        mode="danger"
        title="Are you sure to delete this plan?"
        subtitle="This action can't be undone."
        noteMessage=""
        className=""
        confirmationButtons={[
          <EverButton
            type="ghost"
            color="base"
            key="cancel"
            onClick={() => setShowDeleteConfirm(false)}
          >
            Cancel
          </EverButton>,
          <EverButton
            key="delete"
            color="error"
            onClick={() => {
              handleDelete(planId);
              setShowDeleteConfirm(false);
            }}
          >
            Yes, delete
          </EverButton>,
        ]}
      />

      <CreateUpdatePlanModal
        open={showEditModal}
        setOpen={setShowEditModal}
        editMode={true}
        planId={planId}
        onPlanUpdated={(updatedPlanId) => {
          setShowEditModal(false);
          navigate(`/planning/${updatedPlanId}`);
        }}
      />
    </div>
  );
};

TerritoryPlanDetails.propTypes = {
  planData: PropTypes.shape({
    tplanId: PropTypes.string.isRequired,
    effectiveStartDate: PropTypes.string,
    effectiveEndDate: PropTypes.string,
  }),
};

export default TerritoryPlanDetails;
