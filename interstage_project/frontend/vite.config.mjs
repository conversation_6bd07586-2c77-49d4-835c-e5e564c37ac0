// vite.config.js
import react from "@vitejs/plugin-react";
import dotenv from "dotenv";
import { defineConfig } from "vite";
import svgr from "vite-plugin-svgr";
import tsconfigPaths from "vite-tsconfig-paths";
import eslint from "vite-plugin-eslint";

import {
  proxyMiddleware,
  htmlPlugin,
  cssReplaceS3Location,
  treatJsxAsJs,
} from "./vite.middleware.js";

dotenv.config();

let port = process.env.PORT || 3000;

const define = {};
for (const k in process.env) {
  define[`process.env.${k}`] = JSON.stringify(process.env[k]);
}

export default defineConfig({
  publicDir: "public/static",
  plugins: [
    htmlPlugin(),
    treatJsxAsJs(),
    tsconfigPaths(),
    {
      ...eslint({
        failOnWarning: false,
        failOnError: false,
      }),
      apply: "serve",
      enforce: "post",
    },
    svgr(),
    react({ jsxRuntime: "classic" }),
    cssReplaceS3Location(),
    proxyMiddleware({
      proxy: "http://localhost:8000",
    }),
  ],
  css: {
    devSourcemap: true,
    preprocessorOptions: {
      scss: {
        api: "modern-compiler",
      },
    },
  },
  optimizeDeps: {
    force: true,
    esbuildOptions: {
      loader: {
        ".js": "jsx",
        ".svg": "dataurl",
      },
    },
  },
  server: {
    host: true,
    port,
    watch: {
      usePolling: true,
    },
  },
  define,
  resolve: {
    alias: {
      // because from perspective of vite the index.js is in src/, unlike esbuild which is in static/
      // we also need to replace the index.js in the html-transform plugin in vite.middleware.js
      // else we get a 404 error
      "%index.js%": "./src/index.js",
    },
  },
});
