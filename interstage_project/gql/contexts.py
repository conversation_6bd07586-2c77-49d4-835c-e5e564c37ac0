from django.utils import timezone
from django.utils.functional import cached_property

from spm.graphql.data_loaders import (
    AccessTokenConfigLoader,
    ApprovalInstanceLoader,
    ApprovalStageLoader,
    CanUserManageAdminsLoader,
    CommissionLockLoader,
    CommissionPlanLoader,
    CriteriaLoader,
    CustomerIdLoader,
    CustomFieldLoader,
    CustomObjectVariableLastUpdatedAtLoader,
    CustomObjectVariableLoader,
    CustomPeriodLabelLoader,
    DatabookLoader,
    DatasheetForDBLoader,
    DatasheetLoader,
    DatasheetVariablesLoader,
    DatasheetVariablesWithPermissionLoader,
    DbkdMapForDBLoader,
    DrsUpdatesLoader,
    EmployeeCriteriaLoader,
    EmployeeDrawLoader,
    EmployeeHierarchyLoader,
    EmployeeLoader,
    EmployeeMainAndSpiffPlanLoader,
    EmployeePayoutFrequencyLoader,
    EmployeePayrollLoader,
    EmployeePlanLoader,
    EmployeePlanNameLoader,
    EmployeeReporteesLoader,
    EmployeeSpiffPlanLoader,
    EmployeeUserRoleDetailsLoader,
    EmployeeVariablePayLoader,
    FieldDataTypeLoader,
    FieldsCountLoader,
    FieldsLoader,
    GroupMemberHierarchyLoader,
    GroupMemberLoader,
    InitialSyncDateLoader,
    IntegrationAdditionalDataLoader,
    IntegrationIdLoader,
    IsFivetranSyncLoader,
    IsGroupMemberAllowedLoader,
    LastSyncedTimeLoader,
    LatestSyncRunStatusLoader,
    ManagerDetailsLoader,
    MemberLoader,
    MembershipLoader,
    PayeeMembershipLoader,
    PayeePlanLoader,
    PlanApprovalsDataLoader,
    PlanCriteriaLoader,
    PlanScopeLoader,
    QuotaCategoryDisplayNameMapLoader,
    QuotaCurrencySymbolLoader,
    SourceConnectionIDLoader,
    SourceConnectionNameLoader,
    SourceConnectorLoader,
    SyncedFromTimeLoader,
    SyncEnabledLoader,
    SyncStartTimeLoader,
    TotalCustomObjectVariablesLoader,
    VariableLoader,
)
from spm.graphql.variables_schema.data_loaders import VariableDataTypeLoader


class GQLContext:
    """
    This file defines the GQLContext class, which is responsible for managing the GraphQL context in the application.

    The GQLContext class initializes various data loaders and provides them as cached properties, making them easily accessible throughout the application.

    Attributes:
        request (HttpRequest): The HTTP request object.
        META (dict): The request's META data.
        COOKIES (dict): The request's cookies.
        knowledge_date (datetime): The current date and time.
        client_id (str): The client ID associated with the request.
        domain (str): The domain associated with the request.
        request_id (str): The unique ID of the request.

    Properties:
        user (User): The authenticated user associated with the request.
        employee_payroll_loader (EmployeePayrollLoader): The data loader for employee payroll information.
        employee_hierarchy_loader (EmployeeHierarchyLoader): The data loader for employee hierarchy information.
        employee_plan_loader (EmployeePlanLoader): The data loader for employee plan information.
        employee_payout_frequency_loader (EmployeePayoutFrequencyLoader): The data loader for employee payout frequency information.
        membership_loader (MembershipLoader): The data loader for membership information.
        member_loader (MemberLoader): The data loader for member information.
        group_member_loader (GroupMemberLoader): The data loader for group member information.
        is_group_member_allowed_loader (IsGroupMemberAllowedLoader): The data loader for checking if a user is allowed to be a group member.
        group_member_hierarchy_loader (GroupMemberHierarchyLoader): The data loader for group member hierarchy information.
        variable_loader (VariableLoader): The data loader for variable information.
        variable_data_type_loader (VariableDataTypeLoader): The data loader for variable data type information.
        employee_loader (EmployeeLoader): The data loader for employee information.
        payee_plan_loader (PayeePlanLoader): The data loader for payee plan information.
        plan_criteria_loader (PlanCriteriaLoader): The data loader for plan criteria information.
        employee_draw_loader (EmployeeDrawLoader): The data loader for employee draw information.
        employee_user_role_details_loader (EmployeeUserRoleDetailsLoader): The data loader for employee user role details information.
        can_user_manage_admins_loader (CanUserManageAdminsLoader): The data loader for checking if a user can manage admins.
        employee_reportees_loader (EmployeeReporteesLoader): The data loader for employee reportees information.
        employee_plan_name_loader (EmployeePlanNameLoader): The data loader for employee plan name information.
        employee_variable_pay_loader (EmployeeVariablePayLoader): The data loader for employee variable pay information.
        manager_details_loader (ManagerDetailsLoader): The data loader for manager details information.
        employee_spiff_plan_loader (EmployeeSpiffPlanLoader): The data loader for employee spiff plan information.
        custom_field_loader (CustomFieldLoader): The data loader for custom field information.
        employee_plan_criteria_loader (EmployeeCriteriaLoader): The data loader for employee plan criteria information.
        drs_updates_loader (DrsUpdatesLoader): The data loader for DRS updates information.
        datasheet_loader (DatasheetLoader): The data loader for datasheet information.
        datasheet_for_db_loader (DatasheetForDBLoader): The data loader for datasheet for DB information.
        custom_object_variable_loader (CustomObjectVariableLoader): The data loader for custom object variable information.
        datasheet_variable_loader (DatasheetVariablesLoader): The data loader for datasheet variable information.
        datasheet_variable_with_permission_loader (DatasheetVariablesWithPermissionLoader): The data loader for datasheet variables with permission information.
        payee_membership_loader (PayeeMembershipLoader): The data loader for payee membership information.
        dbkd_for_db_loader (DbkdMapForDBLoader): The data loader for DBKD for DB information.
        commission_plan_loader (CommissionPlanLoader): The data loader for commission plan information.
        criteria_loader (CriteriaLoader): The data loader for criteria information.
        databook_loader (DatabookLoader): The data loader for databook information.
        approval_stage_loader (ApprovalStageLoader): The data loader for approval stage information.
        approval_instance_loader (ApprovalInstanceLoader): The data loader for approval instance information.
        commission_lock_loader (CommissionLockLoader): The data loader for commission lock information.
        access_token_config_loader (AccessTokenConfigLoader): The data loader for access token config information.
        quota_category_display_name_map_loader (QuotaCategoryDisplayNameMapLoader): The data loader for quota category display name map information.
        quota_currency_symbol_loader (QuotaCurrencySymbolLoader): The data loader for quota currency symbol information.
        custom_period_label_loader (CustomPeriodLabelLoader): The data loader for custom period label information.
    """

    def __init__(self, request):
        self.request = request
        self.META = request.META
        self.COOKIES = request.COOKIES
        self.knowledge_date = timezone.now()
        self.client_id = getattr(request, "client_id", None)
        self.domain = getattr(request, "domain", None)
        self.request_id = request.request_id

    @cached_property
    def user(self):
        return self.request.user

    @cached_property
    def master_user(self):
        return self.request.master_user

    @cached_property
    def employee_payroll_loader(self):
        return EmployeePayrollLoader(self)

    @cached_property
    def employee_hierarchy_loader(self):
        return EmployeeHierarchyLoader(self)

    @cached_property
    def employee_plan_loader(self):
        return EmployeePlanLoader(self)

    @cached_property
    def employee_payout_frequency_loader(self):
        return EmployeePayoutFrequencyLoader(self)

    @cached_property
    def membership_loader(self):
        return MembershipLoader(self)

    @cached_property
    def member_loader(self):
        return MemberLoader(self)

    @cached_property
    def group_member_loader(self):
        return GroupMemberLoader(self)

    @cached_property
    def is_group_member_allowed_loader(self):
        return IsGroupMemberAllowedLoader(self)

    @cached_property
    def group_member_hierarchy_loader(self):
        return GroupMemberHierarchyLoader(self)

    @cached_property
    def variable_loader(self):
        return VariableLoader(self)

    @cached_property
    def variable_data_type_loader(self):
        return VariableDataTypeLoader(self)

    @cached_property
    def employee_loader(self):
        return EmployeeLoader(self)

    @cached_property
    def payee_plan_loader(self):
        return PayeePlanLoader(self)

    @cached_property
    def plan_criteria_loader(self):
        return PlanCriteriaLoader(self)

    @cached_property
    def plan_scope_loader(self):
        return PlanScopeLoader(self)

    @cached_property
    def plan_approvals_data_loader(self):
        return PlanApprovalsDataLoader(self)

    @cached_property
    def employee_draw_loader(self):
        return EmployeeDrawLoader(self)

    @cached_property
    def employee_user_role_details_loader(self):
        return EmployeeUserRoleDetailsLoader(self)

    @cached_property
    def can_user_manage_admins_loader(self):
        return CanUserManageAdminsLoader(self)

    @cached_property
    def employee_reportees_loader(self):
        return EmployeeReporteesLoader(self)

    @cached_property
    def employee_plan_name_loader(self):
        return EmployeePlanNameLoader(self)

    @cached_property
    def employee_variable_pay_loader(self):
        return EmployeeVariablePayLoader(self)

    @cached_property
    def manager_details_loader(self):
        return ManagerDetailsLoader(self)

    @cached_property
    def employee_spiff_plan_loader(self):
        return EmployeeSpiffPlanLoader(self)

    @cached_property
    def employee_main_and_spiff_plan_loader(self):
        return EmployeeMainAndSpiffPlanLoader(self)

    @cached_property
    def custom_field_loader(self):
        return CustomFieldLoader(self)

    @cached_property
    def employee_plan_criteria_loader(self):
        return EmployeeCriteriaLoader(self)

    @cached_property
    def drs_updates_loader(self):
        return DrsUpdatesLoader(self)

    @cached_property
    def datasheet_loader(self):
        return DatasheetLoader(self)

    @cached_property
    def datasheet_for_db_loader(self):
        return DatasheetForDBLoader(self)

    @cached_property
    def custom_object_variable_loader(self):
        return CustomObjectVariableLoader(self)

    @cached_property
    def datasheet_variable_loader(self):
        return DatasheetVariablesLoader(self)

    @cached_property
    def datasheet_variable_with_permission_loader(self):
        return DatasheetVariablesWithPermissionLoader(self)

    @cached_property
    def payee_membership_loader(self):
        return PayeeMembershipLoader(self)

    @cached_property
    def dbkd_for_db_loader(self):
        return DbkdMapForDBLoader(self)

    @cached_property
    def commission_plan_loader(self):
        return CommissionPlanLoader(self)

    @cached_property
    def criteria_loader(self):
        return CriteriaLoader(self)

    @cached_property
    def databook_loader(self):
        return DatabookLoader(self)

    @cached_property
    def approval_stage_loader(self):
        return ApprovalStageLoader(self)

    @cached_property
    def approval_instance_loader(self):
        return ApprovalInstanceLoader(self)

    @cached_property
    def commission_lock_loader(self):
        return CommissionLockLoader(self)

    @cached_property
    def access_token_config_loader(self):
        return AccessTokenConfigLoader(self)

    @cached_property
    def total_custom_object_variables_loader(self):
        return TotalCustomObjectVariablesLoader(self)

    @cached_property
    def custom_object_variable_last_updated_at_loader(self):
        return CustomObjectVariableLastUpdatedAtLoader(self)

    @cached_property
    def quota_category_display_name_map_loader(self):
        return QuotaCategoryDisplayNameMapLoader(self)

    @cached_property
    def quota_currency_symbol_loader(self):
        return QuotaCurrencySymbolLoader(self)

    @cached_property
    def custom_period_label_loader(self):
        return CustomPeriodLabelLoader(self)

    @cached_property
    def fields_count_loader(self):
        return FieldsCountLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def source_connector_loader(self):
        return SourceConnectorLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def source_connection_name_loader(self):
        return SourceConnectionNameLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def sync_enabled_loader(self):
        return SyncEnabledLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def is_fivetran_sync_loader(self):
        return IsFivetranSyncLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def last_synced_time_loader(self):
        return LastSyncedTimeLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def initial_sync_date_loader(self):
        return InitialSyncDateLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def sync_start_time_loader(self):
        return SyncStartTimeLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def integration_additional_data_loader(self):
        return IntegrationAdditionalDataLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def integration_id_loader(self):
        return IntegrationIdLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def fields_loader(self):
        return FieldsLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def field_data_type_loader(self):
        return FieldDataTypeLoader()  # type: ignore

    @cached_property
    def latest_sync_run_status_loader(self):
        return LatestSyncRunStatusLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def source_connection_id_loader(self):
        return SourceConnectionIDLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def synced_from_time_loader(self):
        return SyncedFromTimeLoader(client_id=self.client_id)  # type: ignore

    @cached_property
    def customer_id_loader(self):
        return CustomerIdLoader(self)
