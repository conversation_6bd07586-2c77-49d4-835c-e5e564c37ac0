# SHA1:e44c38c38cdaf7f5528d93fde2a34e184f036414
#
# This file is autogenerated by pip-compile-multi
# To update, run:
#
#    pip-compile-multi
#
allure-pytest==2.9.45
    # via -r requirements/dev.in
allure-python-commons==2.9.45
    # via allure-pytest
appnope==0.1.4
    # via
    #   ipykernel
    #   ipython
argon2-cffi==23.1.0
    # via notebook
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
astroid==2.15.8
    # via pylint
asttokens==3.0.0
    # via
    #   icecream
    #   stack-data
attrs==25.3.0
    # via
    #   allure-python-commons
    #   jsonschema
    #   outcome
    #   pytest
    #   referencing
    #   trio
awscli==1.40.21
    # via -r requirements/dev.in
backcall==0.2.0
    # via ipython
bandit==1.7.10
    # via -r requirements/dev.in
beautifulsoup4==4.13.4
    # via nbconvert
black==24.3.0
    # via -r requirements/dev.in
bleach[css]==6.2.0
    # via nbconvert
boto3==1.38.22
    # via -r requirements/dev.in
botocore==1.38.22
    # via
    #   awscli
    #   boto3
    #   s3transfer
certifi==2025.4.26
    # via
    #   requests
    #   urllib3
cffi==1.17.1
    # via
    #   argon2-cffi-bindings
    #   cryptography
cfgv==3.4.0
    # via pre-commit
chardet==5.2.0
    # via diff-cover
charset-normalizer==3.4.2
    # via requests
chromedriver-autoinstaller==0.3.1
    # via -r requirements/dev.in
click==8.2.1
    # via
    #   black
    #   import-linter
    #   typer
colorama==0.4.6
    # via
    #   awscli
    #   icecream
    #   pytest-depends
comm==0.2.2
    # via ipykernel
coverage[toml]==7.8.1
    # via
    #   pytest-cov
    #   pytest-testmon
cryptography==45.0.2
    # via
    #   pyopenssl
    #   urllib3
debugpy==1.8.14
    # via
    #   -r requirements/dev.in
    #   ipykernel
decorator==5.2.1
    # via ipython
deepdiff==6.7.1
    # via -r requirements/dev.in
defusedxml==0.7.1
    # via nbconvert
diff-cover==7.5.0
    # via -r requirements/dev.in
dill==0.4.0
    # via pylint
distlib==0.3.9
    # via virtualenv
docker==6.0.1
    # via -r requirements/dev.in
docutils==0.19
    # via awscli
exceptiongroup==1.3.0
    # via
    #   pytest
    #   trio
    #   trio-websocket
execnet==2.1.1
    # via pytest-xdist
executing==2.2.0
    # via
    #   icecream
    #   stack-data
fastjsonschema==2.21.1
    # via nbformat
filelock==3.18.0
    # via virtualenv
future-fstrings==1.2.0
    # via pytest-depends
grimp==3.9
    # via import-linter
h11==0.16.0
    # via wsproto
icecream==2.1.3
    # via -r requirements/dev.in
identify==2.6.10
    # via pre-commit
idna==3.10
    # via
    #   requests
    #   trio
    #   urllib3
import-linter==2.0
    # via -r requirements/dev.in
iniconfig==2.1.0
    # via pytest
invoke==1.7.0
    # via -r requirements/dev.in
ipykernel==6.29.5
    # via notebook
ipython==8.14.0
    # via
    #   -r requirements/dev.in
    #   ipykernel
ipython-genutils==0.2.0
    # via notebook
isort==5.12.0
    # via
    #   -r requirements/dev.in
    #   pylint
jedi==0.19.2
    # via ipython
jinja2==3.1.6
    # via
    #   diff-cover
    #   nbconvert
    #   notebook
    #   pytest-html
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
joblib==1.5.0
    # via grimp
jsonschema==4.23.0
    # via nbformat
jsonschema-specifications==2025.4.1
    # via jsonschema
jupyter-client==8.6.3
    # via
    #   ipykernel
    #   nbclient
    #   notebook
jupyter-core==5.7.2
    # via
    #   ipykernel
    #   jupyter-client
    #   nbclient
    #   nbconvert
    #   nbformat
    #   notebook
jupyterlab-pygments==0.3.0
    # via nbconvert
lazy-object-proxy==1.11.0
    # via astroid
markdown-it-py==2.2.0
    # via rich
markupsafe==3.0.2
    # via
    #   jinja2
    #   nbconvert
matplotlib-inline==0.1.7
    # via
    #   ipykernel
    #   ipython
mccabe==0.7.0
    # via pylint
mdurl==0.1.2
    # via markdown-it-py
mistune==3.1.3
    # via nbconvert
mypy-extensions==1.1.0
    # via black
nbclient==0.10.2
    # via nbconvert
nbconvert==7.16.6
    # via notebook
nbformat==5.10.4
    # via
    #   nbclient
    #   nbconvert
    #   notebook
nest-asyncio==1.6.0
    # via
    #   ipykernel
    #   notebook
networkx==3.4.2
    # via pytest-depends
nodeenv==1.9.1
    # via pre-commit
notebook==6.4.12
    # via -r requirements/dev.in
ordered-set==4.1.0
    # via deepdiff
outcome==1.3.0.post0
    # via
    #   trio
    #   trio-websocket
packaging==25.0
    # via
    #   black
    #   docker
    #   ipykernel
    #   nbconvert
    #   pytest
pandocfilters==1.5.1
    # via nbconvert
parso==0.8.4
    # via jedi
pathspec==0.12.1
    # via black
pbr==6.1.1
    # via stevedore
pexpect==4.9.0
    # via ipython
pickleshare==0.7.5
    # via ipython
pillow==11.2.1
    # via pytest-html-reporter
platformdirs==4.3.8
    # via
    #   black
    #   jupyter-core
    #   pylint
    #   virtualenv
pluggy==1.6.0
    # via
    #   allure-python-commons
    #   diff-cover
    #   pytest
pre-commit==4.2.0
    # via -r requirements/dev.in
prometheus-client==0.22.0
    # via notebook
prompt-toolkit==3.0.51
    # via ipython
psutil==7.0.0
    # via ipykernel
ptyprocess==0.7.0
    # via
    #   pexpect
    #   terminado
pure-eval==0.2.3
    # via stack-data
pyasn1==0.6.1
    # via rsa
pycparser==2.22
    # via cffi
pygments==2.19.1
    # via
    #   diff-cover
    #   icecream
    #   ipython
    #   nbconvert
    #   rich
pylint==2.17.7
    # via
    #   -r requirements/dev.in
    #   pylint-django
    #   pylint-plugin-utils
    #   pylint-pytest
    #   pylint-unittest
pylint-django==2.5.3
    # via -r requirements/dev.in
pylint-plugin-utils==0.7
    # via
    #   -r requirements/dev.in
    #   pylint-django
pylint-pytest==1.1.2
    # via -r requirements/dev.in
pylint-unittest==0.1.3
    # via -r requirements/dev.in
pyopenssl==25.1.0
    # via urllib3
pytest==7.2.0
    # via
    #   -r requirements/dev.in
    #   allure-pytest
    #   pylint-pytest
    #   pytest-cov
    #   pytest-depends
    #   pytest-django
    #   pytest-html
    #   pytest-html-reporter
    #   pytest-metadata
    #   pytest-testmon
    #   pytest-xdist
pytest-cov==3.0.0
    # via -r requirements/dev.in
pytest-depends==1.0.1
    # via -r requirements/dev.in
pytest-django==4.5.2
    # via -r requirements/dev.in
pytest-html==4.0.1
    # via -r requirements/dev.in
pytest-html-reporter==0.2.6
    # via -r requirements/dev.in
pytest-metadata==3.1.1
    # via pytest-html
pytest-testmon==2.0.15
    # via -r requirements/dev.in
pytest-xdist==3.3.1
    # via -r requirements/dev.in
python-dateutil==2.9.0.post0
    # via
    #   botocore
    #   jupyter-client
python-dotenv==0.20.0
    # via -r requirements/dev.in
pyyaml==6.0.1
    # via
    #   -r requirements/dev.in
    #   awscli
    #   bandit
    #   pre-commit
pyzmq==26.4.0
    # via
    #   ipykernel
    #   jupyter-client
    #   notebook
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.2
    # via
    #   -r requirements/dev.in
    #   docker
rich==13.3.4
    # via
    #   -r requirements/dev.in
    #   bandit
rpds-py==0.25.1
    # via
    #   jsonschema
    #   referencing
rsa==4.7.2
    # via awscli
ruff==0.1.15
    # via -r requirements/dev.in
s3transfer==0.13.0
    # via
    #   awscli
    #   boto3
selenium==4.1.0
    # via -r requirements/dev.in
send2trash==1.8.3
    # via notebook
six==1.17.0
    # via
    #   allure-pytest
    #   allure-python-commons
    #   python-dateutil
sniffio==1.3.1
    # via trio
sortedcontainers==2.4.0
    # via trio
soupsieve==2.7
    # via beautifulsoup4
stack-data==0.6.3
    # via ipython
stevedore==5.4.1
    # via bandit
stringcase==1.2.0
    # via -r requirements/dev.in
terminado==0.18.1
    # via notebook
tinycss2==1.4.0
    # via bleach
tomli==2.2.1
    # via
    #   black
    #   coverage
    #   import-linter
    #   pylint
    #   pytest
tomlkit==0.13.2
    # via pylint
tornado==6.5.1
    # via
    #   ipykernel
    #   jupyter-client
    #   notebook
    #   terminado
traitlets==5.14.3
    # via
    #   comm
    #   ipykernel
    #   ipython
    #   jupyter-client
    #   jupyter-core
    #   matplotlib-inline
    #   nbclient
    #   nbconvert
    #   nbformat
    #   notebook
trio==0.30.0
    # via
    #   selenium
    #   trio-websocket
trio-websocket==0.12.2
    # via selenium
typer==0.9.0
    # via -r requirements/dev.in
typing-extensions==4.13.2
    # via
    #   astroid
    #   beautifulsoup4
    #   black
    #   exceptiongroup
    #   grimp
    #   import-linter
    #   mistune
    #   pyopenssl
    #   referencing
    #   typer
urllib3[secure]==1.26.20
    # via
    #   botocore
    #   docker
    #   requests
    #   selenium
urllib3-secure-extra==0.1.0
    # via urllib3
virtualenv==20.31.2
    # via pre-commit
wcwidth==0.2.13
    # via prompt-toolkit
webencodings==0.5.1
    # via
    #   bleach
    #   tinycss2
websocket-client==1.8.0
    # via docker
wrapt==1.17.2
    # via astroid
wsproto==1.2.0
    # via trio-websocket

# The following packages are considered to be unsafe in a requirements file:
# setuptools
