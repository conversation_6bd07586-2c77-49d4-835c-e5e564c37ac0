from promise import Promise

from everstage_admin_backend.accessors.customer_id_management_accessor import (
    CustomerIdManagementAccessor,
)
from spm.accessors.quota_acessors import QuotaAccessor
from spm.graphql.commons import DataLoaderWithContext


class QuotaCategoryDisplayNameMapLoader(DataLoaderWithContext):
    def batch_load_fn(self, keys):
        quota_accessor = QuotaAccessor(self.ctx.client_id)
        quota_display_name_map = quota_accessor.get_quota_category_display_name_map(
            keys
        )
        display_name_map = {
            record["quota_category_name"]: record["display_name"]
            for record in quota_display_name_map
        }
        return Promise.resolve([display_name_map.get(name, name) for name in keys])


class CustomerIdLoader(DataLoaderWithContext):
    def batch_load_fn(self, keys):
        mappings = CustomerIdManagementAccessor().get_all_mappings(keys)
        customer_id_map = {
            mapping.hubspot_company_id: mapping.customer_id for mapping in mappings
        }
        return Promise.resolve([customer_id_map.get(key) for key in keys])
