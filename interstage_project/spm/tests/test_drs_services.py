import base64
import datetime
import os
from unittest import mock
from unittest.mock import <PERSON>Mock, patch

import pytest
from django.db.models.query import QuerySet

from commission_engine.models import Client
from spm.models import DRSUpdates
from spm.services import drs_services
from spm.tests.logger import LoggerUnit
from spm.tests.models import create_client_config


def get_drs_data(drs_dict, validation=True):
    if validation:
        return drs_services.DRSParams(**drs_dict)
    else:
        return drs_services.DRSParams.model_construct(**drs_dict)


@pytest.mark.django_db
@pytest.mark.spm
class TestDrsServices:
    @patch("spm.services.drs_slack_service.notify_query_created")
    def test_persist_drs(self, mock_query):
        mock_query.return_value = None
        encoded_message = base64.b64encode("Test Message".encode("utf-8")).decode(
            "utf-8"
        )
        drs_data = get_drs_data(
            {
                "id": "233bff06-1adc-4eff-b671-e124ea2c399a",
                "assignee": "<EMAIL>",
                "subject": "test",
                "status": "Assigned",
                "category": "General query",
                "category_id": "b82ef8b4-4f28-4ab3-a320-3ed2d9ed77c9",
                "involved_users": ["<EMAIL>"],
                "message": encoded_message,
                "message_markdown": encoded_message,
            }
        )
        resp = drs_services.persist_drs(1, drs_data, LoggerUnit.logger())
        assert resp["success"] is True

    @patch("spm.services.drs_services.notify_query_update_on_slack")
    def test_persist_drs_updates(self, mock_query):
        drs_id = "233bff06-1adc-4eff-b671-e124ea2c399a"
        encoded_message = base64.b64encode("Test Message".encode("utf-8")).decode(
            "utf-8"
        )
        DRSUpdates.objects.create(
            drs_id=drs_id,
            meta={
                "status": {"new_value": "Assigned", "old_value": "Assigned"},
                "assignee": {
                    "new_value": "<EMAIL>",
                    "old_value": "<EMAIL>",
                },
                "category_id": {
                    "new_value": "b82ef8b4-4f28-4ab3-a320-3ed2d9ed77c9",
                    "old_value": "b82ef8b4-4f28-4ab3-a320-3ed2d9ed77c9",
                },
                "category": {
                    "new_value": "General query",
                    "old_value": "General query",
                },
                "involved_users": {
                    "new_value": ["<EMAIL>"],
                    "old_value": ["<EMAIL>"],
                },
            },
            message="Test Message",
            message_markdown="Test Message",
            updated_by="<EMAIL>",
            updated_time=datetime.datetime.now(),
            client_id=1,
            knowledge_begin_date=datetime.datetime.now(),
        )
        drs_update_data = get_drs_data(
            {
                "id": drs_id,
                "assignee": "<EMAIL>",
                "status": "Assigned",
                "subject": "test",
                "category_id": "b82ef8b4-4f28-4ab3-a320-3ed2d9ed77c9",
                "category": "General query",
                "involved_users": ["<EMAIL>"],
                "message": encoded_message,
                "message_markdown": encoded_message,
            },
            validation=False,
        )
        print("DRS UPDATE DATA.. ", drs_update_data)
        mock_query.return_value = None
        resp = drs_services.persist_drs_updates(
            1, drs_id, drs_update_data, LoggerUnit.logger()
        )
        assert resp["success"] is True

    @mock.patch.dict(os.environ, {"ENV": "STAGING"})
    @patch("spm.services.drs_services.CoreAnalytics.send_analytics")
    def test_persist_drs_mock(self, mock_analytics):
        mock_analytics.return_value = None
        encoded_message = base64.b64encode("Test Message".encode("utf-8")).decode(
            "utf-8"
        )
        Client.objects.create(
            client_id=545,
            name="test",
            domain="test",
            logo_url=None,
            auth_connection_name=None,
            connection_type=None,
            base_currency=None,
            fiscal_start_month=None,
            secondary_calculator=None,
            client_notification=True,
            payee_notification=True,
            meta_info={},
            logo_s3_path=None,
            time_zone=None,
            client_features={},
            is_deleted=False,
        )
        drs_services.query_created_mails = MagicMock()
        drs_services.notify_query_created = MagicMock()
        drs_data = get_drs_data(
            {
                "id": "233bff06-1adc-4eff-b671-e124ea2c399a",
                "assignee": "<EMAIL>",
                "subject": "test",
                "status": "Assigned",
                "category_id": "b82ef8b4-4f28-4ab3-a320-3ed2d9ed77c9",
                "category": "General query",
                "involved_users": ["<EMAIL>"],
                "message": encoded_message,
                "message_markdown": encoded_message,
            }
        )
        resp = drs_services.persist_drs(545, drs_data, LoggerUnit.logger())
        drs_services.notify_query_created.assert_called_once()
        assert resp["success"] is True
        drs_services.query_created_mails.assert_called_once()
        mock_analytics.assert_called_once()

    @mock.patch.dict(os.environ, {"ENV": "STAGING"})
    @patch("spm.services.drs_services.CoreAnalytics.send_analytics")
    def test_persist_drs_updates_mock(self, mock_analytics):
        mock_analytics.return_value = None
        encoded_message = base64.b64encode("Test Message".encode("utf-8")).decode(
            "utf-8"
        )
        drs_services.query_update_mails = MagicMock()
        Client.objects.create(
            client_id=545,
            name="test",
            domain="test",
            logo_url=None,
            auth_connection_name=None,
            connection_type=None,
            base_currency=None,
            fiscal_start_month=None,
            secondary_calculator=None,
            client_notification=True,
            payee_notification=True,
            meta_info={},
            logo_s3_path=None,
            time_zone=None,
            client_features={},
            is_deleted=False,
        )
        drs_services.notify_query_update_on_slack = MagicMock()
        drs_id = "233bff06-1adc-4eff-b671-e124ea2c399a"
        DRSUpdates.objects.create(
            drs_id=drs_id,
            meta={
                "status": {"new_value": "Assigned", "old_value": "Assigned"},
                "assignee": {
                    "new_value": "<EMAIL>",
                    "old_value": "<EMAIL>",
                },
                "category_id": {
                    "new_value": "b82ef8b4-4f28-4ab3-a320-3ed2d9ed77c9",
                    "old_value": "b82ef8b4-4f28-4ab3-a320-3ed2d9ed77c9",
                },
                "category": {
                    "new_value": "General query",
                    "old_value": "General query",
                },
                "involved_users": {
                    "new_value": ["<EMAIL>"],
                    "old_value": ["<EMAIL>"],
                },
            },
            message="Test Message",
            message_markdown="Test Message",
            updated_by="<EMAIL>",
            updated_time=datetime.datetime.now(),
            client_id=545,
            knowledge_begin_date=datetime.datetime.now(),
        )
        drs_update_data = get_drs_data(
            {
                "id": drs_id,
                "assignee": "<EMAIL>",
                "status": "Assigned",
                "subject": "test",
                "category_id": "b82ef8b4-4f28-4ab3-a320-3ed2d9ed77c9",
                "category": "General query",
                "involved_users": ["<EMAIL>"],
                "message": encoded_message,
                "message_markdown": encoded_message,
            },
            validation=False,
        )
        resp = drs_services.persist_drs_updates(
            545,
            drs_id,
            drs_update_data,
            LoggerUnit.logger(),
        )
        print(resp)
        assert resp["success"] is True
        drs_services.notify_query_update_on_slack.assert_called_once()
        drs_services.query_update_mails.assert_called_once()
        mock_analytics.assert_called_once()

    @pytest.mark.parametrize(
        "client_id,specific_user,employee_mail",
        [(1, ["<EMAIL>"], "<EMAIL>")],
    )
    def test_get_assignee_user_list_for_specific_user(
        self, client_id, specific_user, employee_mail
    ):
        data = {
            "query_assignee_type": "specific_user",
            "selected_user": specific_user,
            "cc_others": True,
        }
        create_client_config("QuerySetting", data)
        with patch.object(drs_services, "get_email_name_map") as mock_fn:
            drs_services.get_assignee_user_list(client_id, employee_mail)
            mock_fn.assert_called_once_with(client_id, specific_user)

    @pytest.mark.parametrize(
        "client_id,employee_mail,expected_result",
        [
            (
                7008,
                "<EMAIL>",
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "full_name": "Admin test",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "full_name": "Admin test",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "full_name": "chandler c",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "full_name": "henderson h",
                    },
                    {"employee_email_id": "<EMAIL>", "full_name": "henry h"},
                    {"employee_email_id": "<EMAIL>", "full_name": "jack j"},
                    {
                        "employee_email_id": "<EMAIL>",
                        "full_name": "Super Admin test",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "full_name": "Super Admin test",
                    },
                ],
            ),
            (
                7011,
                "<EMAIL>",
                [
                    {
                        "employee_email_id": "<EMAIL>",
                        "full_name": "Active payee2",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "full_name": "Active  payee",
                    },
                    {
                        "employee_email_id": "<EMAIL>",
                        "full_name": "future join",
                    },
                ],
            ),
        ],
    )
    def test_get_assignee_user_list_with_hierarchy(
        self, client_id, employee_mail, expected_result
    ):
        result = drs_services.get_assignee_user_list(client_id, employee_mail)
        assert isinstance(result, QuerySet)
        assert list(result) == expected_result
